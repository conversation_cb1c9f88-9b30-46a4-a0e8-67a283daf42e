// src/lib/firebase.js
const admin = require('firebase-admin');
const path = require('path');
const env = process.env.NODE_ENV || 'dev';

const serviceAccountPath = path.join(
    __dirname,
    '..',
    'config',
    'env',
    env,
    'service_account.json'
);

if (!admin.apps.length) {
    admin.initializeApp({
        credential: admin.credential.cert(serviceAccountPath),
    });
}

module.exports = admin;
