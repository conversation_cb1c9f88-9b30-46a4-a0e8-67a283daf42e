'use strict';

module.exports = ({ strapi }) => {
  // Hook into the documentation generation process
  strapi.hooks.addHook('documentation:generateFullDoc:before', (ctx) => {
    // This hook runs before the full documentation is generated
    // You can modify the OpenAPI spec here
    
    // Add custom schema for booking creation
    if (!ctx.spec.components) {
      ctx.spec.components = {};
    }
    
    if (!ctx.spec.components.schemas) {
      ctx.spec.components.schemas = {};
    }
    
    // Define the BookingCreateRequest schema
    ctx.spec.components.schemas.BookingCreateRequest = {
      type: 'object',
      required: ['data'],
      properties: {
        data: {
          required: ['bookingItems', 'scheduledTime'],
          type: 'object',
          properties: {
            bookingItems: {
              type: 'array',
              items: {
                type: 'object',
                required: ['productVariantId'],
                properties: {
                  productVariantId: {
                    type: 'integer',
                    description: 'ID of the product variant to book'
                  },
                  optionValues: {
                    type: 'array',
                    items: {
                      type: 'integer'
                    },
                    description: 'Array of option value IDs. If option group is single selection, only one option value per group is allowed.'
                  }
                }
              },
              description: 'Array of booking items with product variants and optional option values'
            },
            scheduledTime: {
              type: 'string',
              format: 'date-time',
              description: 'Scheduled time for the booking (must be between now and next 4 hours) in UTC+7 timezone (Bangkok, Hanoi, Jakarta)'
            },
            paymentMethod: {
              type: 'string',
              enum: ['cash', 'momo', 'vnpay'],
              default: 'cash',
              description: 'Payment method for the booking'
            }
          }
        }
      },
      example: {
        data: {
          bookingItems: [
            {
              productVariantId: 1,
              optionValues: [5, 8]
            },
            {
              productVariantId: 2,
              optionValues: [6]
            }
          ],
          scheduledTime: '2023-06-15T14:30:00Z',
          paymentMethod: 'cash'
        }
      }
    };
    
    // Update the POST /bookings endpoint to use our custom schema
    if (ctx.spec.paths && ctx.spec.paths['/bookings'] && ctx.spec.paths['/bookings'].post) {
      ctx.spec.paths['/bookings'].post.requestBody = {
        required: true,
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/BookingCreateRequest'
            }
          }
        }
      };
    }
  });
};
