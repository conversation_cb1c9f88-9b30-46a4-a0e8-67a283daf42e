'use strict';

const { parse } = require('json2csv');
const archiver = require('archiver');
const fs = require('fs');
const path = require('path');

module.exports = {
    async exportAllCsv(ctx) {
        const collections = {
            bookings: 'api::booking.booking',
            booking_services: 'api::booking-service.booking-service',
            services: 'api::service.service',
            service_variants: 'api::service-variant.service-variant',
            inventories: 'api::inventory.inventory',
            inventory_operations: 'api::inventory-operation.inventory-operation',
            users: 'plugin::users-permissions.user'
        };

        const tmpDir = path.join(__dirname, '../../../tmp/export');
        if (!fs.existsSync(tmpDir)) {
            fs.mkdirSync(tmpDir, { recursive: true });
        }

        for (const [name, uid] of Object.entries(collections)) {
            try {
                let records = await strapi.documents(uid).findMany({
                    populate: '*',
                    pagination: { pageSize: 1000 }
                });

                if (!records || records.length === 0) {
                    strapi.log.warn(`⚠️ No data found for ${name}, skipping...`);
                    continue;
                }

                records = cleanData(uid, records);

                const csv = parse(records.map(entry => ({
                    id: entry.id,
                    ...entry
                })));

                const filePath = path.join(tmpDir, `${name}.csv`);
                fs.writeFileSync(filePath, csv);
                strapi.log.info(`✅ Exported ${name} to ${filePath}`);
            } catch (err) {
                strapi.log.error(`❌ Failed exporting ${name}: ${err.message}`);
            }
        }

        // Create ZIP archive
        const archivePath = path.join(tmpDir, 'export_all.zip');
        const output = fs.createWriteStream(archivePath);
        const archive = archiver('zip');

        archive.pipe(output);

        for (const name of Object.keys(collections)) {
            const filePath = path.join(tmpDir, `${name}.csv`);
            if (fs.existsSync(filePath)) {
                archive.file(filePath, { name: `${name}.csv` });
            }
        }

        await archive.finalize();

        ctx.set('Content-disposition', 'attachment; filename=export_all.zip');
        ctx.set('Content-Type', 'application/zip');
        ctx.body = fs.createReadStream(archivePath);
    },
    async test(ctx) {
       // test get user
        return strapi.query('plugin::users-permissions.user').findOne({
            where: {
                id: {
                    $eq: 1
                }
            }
        });
    }
};
function cleanData(col, records) {
    switch (col) {
        case 'api::inventory.inventory':
            return records.map(record => ({
                id: record.id,
                serviceVariant: record.serviceVariant?.name ?? '',
                description: record.description ?? '',
                quantity: record.quantity,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'api::inventory-operation.inventory-operation':
            return records.map(record => ({
                id: record.id,
                operation: record.operation,
                quantityAdjustment: record.quantityAdjustment,
                explanation: record.explanation ?? '',
                user: record.user?.username ?? '',
                createdAt: record.createdAt,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'api::booking.booking':
            return records.map(record => ({
                id: record.id,
                code: record.code,
                user: record.user?.username ?? '',
                status: record.bookingStatus,
                paymentStatus: record.paymentStatus,
                totalPrice: record.totalPrice,
                currency: record.currency,
                scheduledTime: record.scheduledTime,
                completedTime: record.completedTime,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'api::booking-service.booking-service':
            return records.map(record => ({
                id: record.id,
                bookingCode: record.booking?.code ?? '',
                serviceVariant: record.serviceVariant?.name ?? '',
                quantity: record.quantity,
                snapshotPrice: record.snapshotPrice,
                totalPrice: record.totalPrice,
                currency: record.currency,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'api::service.service':
            return records.map(record => ({
                id: record.id,
                name: record.name,
                description: record.description ?? '',
                type: record.type,
                availability: record.availability,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'api::service-variant.service-variant':
            return records.map(record => ({
                id: record.id,
                name: record.name,
                service: record.service?.name ?? '',
                price: record.price,
                currency: record.currency,
                metadata: JSON.stringify(record.metadata),
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        case 'plugin::users-permissions.user':
            return records.map(record => ({
                id: record.id,
                username: record.username,
                email: record.email,
                role: record.role?.name ?? '',
                confirmed: record.confirmed,
                blocked: record.blocked,
                createdAt: record.createdAt,
                createdBy: record.createdBy?.email ?? '',
                updatedBy: record.updatedBy?.email ?? '',
            }));

        default:
            return records; // fallback: raw
    }
}
