{"kind": "collectionType", "collectionName": "devices", "info": {"singularName": "device", "pluralName": "devices", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "fcmToken": {"type": "string", "required": true}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "devices"}}}