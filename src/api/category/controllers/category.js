'use strict';

const {yup} = require("strapi-utils");
const {hasNoDuplicates} = require("../../utils/common");
/**
 * category controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::category.category', ({ strapi }) => ({
    async create(ctx) {
        const schema = yup.object().shape({
            name: yup.string().required(),
            description: yup.string()
        }).noUnknown();
        try {
            await schema.validate(ctx.request.body.data);
        } catch (e) {
            return ctx.badRequest('Invalid request body');
        }

        return super.create(ctx);
    },
    async update(ctx) {
        const schema = yup.object().shape({
            name: yup.string(),
            description: yup.string(),
            products: yup.array().of(yup.string())
        }).noUnknown();
        try {
            await schema.validate(ctx.request.body.data);
        } catch (e) {
            return ctx.badRequest('Invalid request body');
        }

        const products = ctx.request.body.data.products || [];

        if(!hasNoDuplicates(products)) {
            return ctx.badRequest('Products must be unique')
        }

        return super.update(ctx);
    }
}))
