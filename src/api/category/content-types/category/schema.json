{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "category", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "description": {"type": "string"}, "productVariants": {"type": "relation", "relation": "oneToMany", "target": "api::product-variant.product-variant", "mappedBy": "category"}}}