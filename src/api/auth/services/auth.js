'use strict'
const admin = require('../../../firebase')
const twilio = require('twilio')
const {deleteAccountOperation, confirmAccountOperation} = require("../controllers/auth")
const twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)
const sgMail = require('@sendgrid/mail');
const crypto = require('crypto');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

function generateOtp(length = 6) {
    const digits = '**********';
    let otp = '';
    for (let i = 0; i < length; i++) {
        otp += digits[Math.floor(Math.random() * 10)];
    }
    return otp;
}

async function createService() {
    const service = await twilioClient.verify.v2.services.create({
        friendlyName: 'Charge & Chill',
    })

    console.log(service.sid)
}

createService()

module.exports = {
    verifyToken(token) {
        return admin.auth().verifyIdToken(token)
    },
    async getOrCreateUserWithFirebaseCredentials(ctx, decodedToken) {
        const {email, name} = decodedToken
        const {sign_in_provider} = decodedToken.firebase
        let user = await strapi.query('plugin::users-permissions.user').findOne({
            where: {
                email: {
                    $eq: email,
                },
            },
            populate: {
                role: true
            }
        })
        const unverifiedCustomerRoleId = process.env.UNVERIFIED_CUSTOMER_ROLE_ID

        const unverifiedCustomerRole = await strapi.query('plugin::users-permissions.role').findOne({
            where: {
                id: unverifiedCustomerRoleId
            }
        })

        console.log(unverifiedCustomerRole)

        if (!user) {
            user = await strapi.query('plugin::users-permissions.user').create({
                data: {
                    email: email,
                    username: email,
                    displayName: name,
                    provider: sign_in_provider,
                    confirmed: true,
                    role: unverifiedCustomerRole
                },
                populate: {
                    role: true
                }
            })
        }

        const jwtService = strapi.plugins['users-permissions'].services.jwt
        const schema = strapi.getModel('plugin::users-permissions.user')
        return {
            jwt: jwtService.issue({id: user.id}),
            user: await strapi.contentAPI.sanitize.output(user, schema)
        }
    },
    sendPhoneOtp(phone) {
        return twilioClient.verify.v2.services(process.env.TWILIO_SERVICE_SID).verifications
            .create({to: phone, channel: 'sms'})
    },
    sendEmailOtp(email) {
        return sendDeleteAccountOtp('<EMAIL>')
    },
    verifyTwilioOtp(code, sId) {
        console.log(`TWILIO_SERVICE_SID`, process.env.TWILIO_SERVICE_SID)
        return twilioClient.verify.v2.services(process.env.TWILIO_SERVICE_SID).verificationChecks.create({
            'code': code,
            'verificationSid': sId
        })
    },
    findVerifiedPhone(phone, dialCode) {
        return strapi.documents('api::phone.phone').findOne({
            where: {
                phoneNumber: {
                    $eq: phone
                },
                dialCode: {
                    $eq: dialCode
                },
            },
            populate: {
                user: true
            }
        })
    },
    async createPhoneVerificationEntity(user, type, verificationSid, dialCode, phoneNumber) {
        await strapi.query('api::verification.verification').delete({
            where: {
                user: {
                    id: user.id
                },
                type: {
                    $eq: type
                }
            }
        })
        return strapi.query('api::verification.verification').create({
            data: {
                user: user.id,
                type: type,
                verificationStatus: 'pending',
                verificationSid: verificationSid,
                dialCode: dialCode,
                phoneNumber: phoneNumber,
                verificationMethod: 'phone'
            }
        })
    },
    async createEmailVerificationEntity(user, type, otp, otpExpires) {
        await strapi.query('api::verification.verification').delete({
            where: {
                user: {
                    id: user.id
                },
                type: {
                    $eq: type
                }
            }
        })
        return strapi.query('api::verification.verification').create({
            data: {
                user: user.id,
                type: type,
                deleteAccountOtp: otp,
                otpExpires: otpExpires,
                verificationStatus: 'pending',
                verificationMethod: 'email'
            }
        })
    },
    async approveVerification(verification) {
        const result = await strapi.query('api::verification.verification').update({
            where: {
                id: verification.id
            },
            data: {
                verificationStatus: 'approved'
            },
            populate: {
                user: true
            }
        })
        if (result.operation === confirmAccountOperation) {
            await createPhone(verification.user, verification.phoneNumber, verification.dialCode)
        }
        return result
    },
    async verifyUser(ctx) {
        const verifiedCustomerRoleId = process.env.VERIFIED_CUSTOMER_ROLE_ID
        const verifiedCustomerRole = await strapi.query('plugin::users-permissions.role').findOne({
            where: {
                id: verifiedCustomerRoleId
            }
        })

        return strapi.query('plugin::users-permissions.user').update({
            where: {
                id: ctx.state.user.id
            },
            data: {
                role: verifiedCustomerRole
            },
            populate: {
                role: true
            }
        })
    },
    async findVerificationByType(user, type) {
        return strapi.query('api::verification.verification').findOne({
            where: {
                user: {
                    id: user.id
                },
                type: {
                    $eq: type
                }
            }
        })
    },
    async pruneVerification(user) {
        return strapi.query('api::verification.verification').delete({
            where: {
                user: {
                    id: user.id
                }
            }
        })
    },
    async deleteUser(user) {
        const randomizedEmail = `${user.username}-${Date.now()}@deleted.com`
        await strapi.query('api::phone.phone').delete({
            where: {
                user: {
                    id: user.id
                }
            }
        })
        await strapi.query('plugin::users-permissions.user').update({
            where: {
                id: user.id
            },
            data: {
                deleted: true,
                email: randomizedEmail,
                role: null,
                username: randomizedEmail,
                displayName: randomizedEmail,
            }
        })
        return true
    }
}

async function createPhone(user, phoneNumber, dialCode) {
    return await strapi.query('api::phone.phone').create({
        data: {
            user: user.id,
            phoneNumber: phoneNumber,
            dialCode: dialCode
        }
    })
}

async function sendDeleteAccountOtp(toEmail, {otpLength = 6, expiresInMinutes = 10} = {}) {
    const otp = generateOtp(otpLength);
    const expiresAt = new Date(Date.now() + expiresInMinutes * 60000)
        .toLocaleString('vi-VN', {hour12: false});

    const msg = {
        to: toEmail,
        from: '<EMAIL>', // Địa chỉ đã verify trong SendGrid
        subject: 'Mã Xác Thực (OTP) xoá tài khoản',
        text: `
Xin chào,

Mã xác thực để xoá tài khoản của bạn là: ${otp}

Mã này có hiệu lực đến ${expiresAt} (trong vòng ${expiresInMinutes} phút).
Nếu bạn không yêu cầu xoá tài khoản, vui lòng bỏ qua email này.

Trân trọng,
Đội ngũ Hỗ trợ
    `,
        html: `
      <div style="font-family:Arial,sans-serif;line-height:1.6;">
        <p>Xin chào,</p>
        <p>
          Mã xác thực để <strong>xoá tài khoản</strong> của bạn là:<br>
          <span style="display:inline-block;padding:8px 12px;
            background:#f0f0f0;border-radius:4px;
            font-size:1.25em;letter-spacing:2px;">${otp}</span>
        </p>
        <p>
          Mã này có hiệu lực đến <strong>${expiresAt}</strong>
          (trong vòng ${expiresInMinutes} phút).
        </p>
        <p>
          Nếu bạn không yêu cầu xoá tài khoản, vui lòng bỏ qua email này.
        </p>
        <hr>
        <p style="color:#555;font-size:0.9em;">
          Đây là email tự động, vui lòng không trả lời.
        </p>
        <p style="color:#555;font-size:0.9em;">
          Trân trọng,<br>
          Đội ngũ Hỗ trợ
        </p>
      </div>
    `,
    };

    try {
        const [response] = await sgMail.send(msg)
        console.log(`Email sent to ${toEmail}. StatusCode:`, response.statusCode)
        return {otp: otp, expires: expiresAt, success: true}
    } catch (error) {
        throw error
    }
}

