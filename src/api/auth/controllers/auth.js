'use strict'

/**
 * A set of functions called "actions" for `auth`
 */

const yup = require("yup")
const minOtpSendInterval = 120 // seconds

const operationValidatedMap = {
    'confirmAccount': 'Phone is already verified',
    'deleteAccount': 'User already deleted'
}

const confirmAccountOperation = 'confirmAccount'
const deleteAccountOperation = 'deleteAccount'
// no dial code
const phoneRegexWithoutDialCode = /^(0|\+84|84)(3|5|7|8|9)[0-9]{8}$/


const validOperations = [
    confirmAccountOperation,
    deleteAccountOperation
]

module.exports = {
    deleteAccountOperation,
    confirmAccountOperation,
    social: async (ctx, next) => {
        try {
            const schema = yup.object().shape({
                provider: yup.string().required(),
                token: yup.string().required()
            })
            try {
                await schema.validate(ctx.request.body)
            } catch (e) {
                ctx.badRequest(e, {errors: e.errors})
            }
            const {token} = ctx.request.body
            let decodedToken

            try {
                decodedToken = await strapi.service('api::auth.auth').verifyToken(token)
            } catch (e) {
                return ctx.unauthorized('Invalid token')
            }

            const response = await strapi.service('api::auth.auth').getOrCreateUserWithFirebaseCredentials(ctx, decodedToken)
            ctx.status = 200
            ctx.body = response
        } catch (e) {
            ctx.badRequest(e, {errors: e.errors})
        }
    },
    requestAccountOtp: async (ctx, next) => {
        try {
            const schema = yup.object().shape({
                phoneNumber: yup.string().required(),
                dialCode: yup.string().oneOf([
                    '+84'
                ]).required(),
                operation: yup.string().oneOf(validOperations).required()
            })
            try {
                await schema.validate(ctx.request.body)
            } catch (e) {
                return ctx.badRequest(e, {errors: e.errors})
            }

            const phoneNumber = ctx.request.body.phoneNumber
            const dialCode = ctx.request.body.dialCode
            const phone = `${dialCode}${phoneNumber}`
            const operation = ctx.request.body.operation
            let verificationMethod = ''
            const unverifiedCustomerRoleId = process.env.UNVERIFIED_CUSTOMER_ROLE_ID
            console.log(`roleId`, unverifiedCustomerRoleId)
            console.log(`user role`, ctx.state.user.role.id)
            switch (ctx.request.body.operation) {
                case confirmAccountOperation:
                    if (ctx.state.user.role && `${ctx.state.user.role.id}` !== `${unverifiedCustomerRoleId}`) {
                        return ctx.badRequest(operationValidatedMap[operation])
                    }
                    // check if phone already verified
                    const findVerifiedPhone = await strapi.service('api::auth.auth').findVerifiedPhone(phone, dialCode)
                    // check if phone already verified by current user

                    if (findVerifiedPhone) {
                        return ctx.badRequest(operationValidatedMap[operation])
                    }

                    verificationMethod = 'phone'
                    break
                case deleteAccountOperation:
                    if (`${ctx.state.user.role.id}` !== `${unverifiedCustomerRoleId}`) {
                        verificationMethod = 'phone'
                    } else {
                        verificationMethod = 'email'
                    }

                    if (ctx.state.user.deleted) {
                        return ctx.badRequest(operationValidatedMap[operation])
                    }
            }
            console.log(`verificationMethod`, verificationMethod)

            const existingVerification = await strapi.service('api::auth.auth').findVerificationByType(ctx.state.user, operation)

            if (existingVerification) {
                if (existingVerification.status === 'approved') {
                    return ctx.badRequest(operationValidatedMap[operation])
                } else {
                    const dateCreated = new Date(existingVerification.createdAt)
                    const dateNow = new Date()
                    const diffTime = Math.abs(dateNow - dateCreated)
                    const diffSeconds = Math.ceil(diffTime / 1000)
                    const canSendAt = new Date(dateCreated.getTime() + minOtpSendInterval * 1000)
                    if (diffSeconds < minOtpSendInterval) {
                        return ctx.badRequest('OTP already sent, please wait a few seconds', {
                            'canSendAt': canSendAt.toISOString(),
                        })
                    }
                }
            }

            try {
                let verificationBody
                switch (verificationMethod) {
                    case 'phone':
                        verificationBody = await strapi.service('api::auth.auth').sendPhoneOtp(phone);
                        await strapi.service('api::auth.auth').createPhoneVerificationEntity(ctx.state.user, operation, verificationBody.sid, dialCode, phoneNumber)
                        break
                    case 'email':
                        verificationBody = await strapi.service('api::auth.auth').sendEmailOtp(ctx.state.user.email);
                        await strapi.service('api::auth.auth').createEmailVerificationEntity(ctx.state.user, operation, verificationBody.otp, verificationBody.expires)
                        break
                    default:
                        return ctx.badRequest('Invalid verification method')
                }
            } catch (e) {
                console.log(`e`, e)
                return ctx.badRequest(`Cannot send OTP, ${e.message}`)
            }

            ctx.status = 200
            return ctx.body = {
                'data': {
                    'message': 'OTP sent successfully',
                    'phone': phone,
                    'operation': operation,
                }
            }
        } catch (e) {
            ctx.badRequest(e, {errors: e.errors})
        }
    },
    verifyAccountOtp: async (ctx, next) => {
        try {
            const schema = yup.object().shape({
                code: yup.string().required(),
                operation: yup.string().oneOf(validOperations).required()
            })
            try {
                await schema.validate(ctx.request.body)
            } catch (e) {
                return ctx.badRequest(e, {errors: e.errors})
            }
            const {code, operation} = ctx.request.body

            const existingVerification = await strapi.service('api::auth.auth').findVerificationByType(ctx.state.user, operation)

            if (!existingVerification) {
                return ctx.badRequest('Verification not found')
            }

            const sId = existingVerification.verificationSid
            console.log(`existingVerification`, existingVerification)
            console.log(`operation`, operation)
            if (existingVerification.type !== operation) {
                return ctx.badRequest('Invalid operation')
            }

            let verificationCheck

            try {
                verificationCheck = await strapi.service('api::auth.auth').verifyTwilioOtp(code, sId)
                if (verificationCheck.status !== 'approved') {
                    return ctx.badRequest('Invalid OTP')
                }
                if (operation === 'confirmAccount') {
                    await strapi.service('api::auth.auth').verifyUser(ctx)
                    await strapi.service('api::auth.auth').approveVerification(existingVerification)
                } else {
                    await strapi.service('api::auth.auth').deleteUser(ctx.state.user)
                    await strapi.service('api::auth.auth').approveVerification(existingVerification)
                }
                await strapi.service('api::auth.auth').pruneVerification(ctx.state.user)
                // at this point, otp is verified
                // update status to approved in the verification entity
            } catch (e) {
                return ctx.badRequest(`Cannot verify OTP, ${e.message}`)
            }

            if (verificationCheck.status !== 'approved') {
                return ctx.badRequest('Invalid OTP')
            }

            ctx.status = 200
            return ctx.body = {
                'data': verificationCheck,
            }
        } catch (e) {
            ctx.internalServerError(e, {errors: e.errors})
        }
    },
    test: async (ctx, next) => {
        // test create phone
        return await createPhone(
            ctx.state.user,
            '909090909',
            '+84')
    }
}

