'use strict';

const {yup} = require("strapi-utils");
/**
 * option-group controller
 */

const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::option-group.option-group', ({strapi}) => ({
    async create(ctx) {
        const schema = yup.object().shape({
            fullName: yup.string().required(),
            description: yup.string().required(),
            singleSelect: yup.boolean().default(false),
            options: yup.array().of(yup.string()).default(['Default Variant']).min(1).required(),
            available: yup.boolean(),
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const group = super.create(ctx)
        const options = ctx.request.body.data.options

        await Promise.all(options.map(async option => {
            return await strapi.db.query('api::option-value.option-value').create({
                data: {
                    name: option,
                    group: group.id,
                    priceAdjustment: 0,
                }
            });
        }))

        return {
            data: group
        }
    },
    async update(ctx) {
        const schema = yup.object().shape({
            fullName: yup.string().required(),
            description: yup.string(),
            displayName: yup.string(),
            singleSelect: yup.boolean(),
            available: yup.boolean(),
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        return {
            data: super.update(ctx)
        }
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return {
            data: super.findOne()
        }
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return {
            data: super.find(ctx)
        }
    },
    async delete(ctx) {
        const {id} = ctx.params
        const group = await strapi.db.query('api::option-group.option-group').update({
            where: {
                id: id
            },
            data: {
                deleted: true,
                deletedAt: new Date(),
            }
        })

        await strapi.service('api::option-group.option-group').softDeleteAllValues(group);

        return {
            data: true
        }
    }
}))