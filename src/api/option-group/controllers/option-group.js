'use strict';

const {yup} = require("strapi-utils");
const {hasNoDuplicates} = require("../../utils/common");
/**
 * option-group controller
 */

const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::option-group.option-group', ({strapi}) => ({
    async create(ctx) {
        const schema = yup.object().shape({
            fullName: yup.string().required(),
            description: yup.string().required(),
            singleSelection: yup.boolean().default(false),
            available: yup.boolean(),
            products: yup.array().of(yup.string()).min(1).max(10).required(),
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
            await strapi.service('api::option-group.option-group').validateProducts(ctx.request.body.data.products)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const options = ctx.request.body.data.options ?? ['Default Option']

        const group = await super.create(ctx)

        await Promise.all(options.map(async option => {
            return await strapi.db.query('api::option-value.option-value').create({
                data: {
                    name: option,
                    group: group.id,
                    priceAdjustment: 0,
                    products: ctx.request.body.data.products,
                }
            })
        }))
        return group
    },
    async update(ctx) {
        const schema = yup.object().shape({
            fullName: yup.string(),
            description: yup.string(),
            displayName: yup.string(),
            singleSelection: yup.boolean(),
            available: yup.boolean(),
            products: yup.array().of(yup.string())
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const products = ctx.request.body.data.products
        if (products && hasNoDuplicates(products)) {
            return ctx.badRequest('Products must be unique')
        }

        return super.update(ctx)
    },
    async findOne(ctx) {
        const result = await super.findOne(ctx)
        if (result && result.data && result.data.deleted) {
            return ctx.notFound('This option group has been deleted')
        }
        return result
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: {
                    $eq: false
                }
            }
        }
        return super.find(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params
        const group = await strapi.db.query('api::option-group.option-group').update({
            where: {
                documentId: id
            },
            data: {
                deleted: true,
                deletedAt: new Date(),
            },
            populate: ['options']
        })

        console.log(`delete group`, group)
        await strapi.service('api::option-group.option-group').softDeleteAllValues(group);

        return {
            data: true
        }
    }
}))