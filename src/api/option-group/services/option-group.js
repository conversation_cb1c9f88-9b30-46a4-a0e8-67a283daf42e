'use strict';

/**
 * option-group service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::option-group.option-group', ({ strapi }) => ({
   async softDeleteAllValues(optionGroup) {
        await strapi.db.query('api::option-value.option-value').updateMany({
            where: {
                group: optionGroup.id
            },
            data: {
                deleted: true,
                deletedAt: new Date(),
            }
        });
   }
}))
