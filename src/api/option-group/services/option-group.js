'use strict';

const {hasNoDuplicates} = require("../../utils/common");
/**
 * option-group service
 */

const {createCoreService} = require('@strapi/strapi').factories;

module.exports = createCoreService('api::option-group.option-group', ({strapi}) => ({
    async softDeleteAllValues(optionGroup) {
        console.log(`optionGroup`, optionGroup)
        const values = await strapi.db.query('api::option-value.option-value').findMany({
            where: {
                group: optionGroup.id
            }
        })

        return Promise.all(values.map(async value => {
            return await strapi.db.query('api::option-value.option-value').update({
                where: {
                    id: value.id
                },
                data: {
                    deleted: true,
                    deletedAt: new Date(),
                }
            })
        }))
    },
    async validateProducts(productIds) {
        if (!hasNoDuplicates(productIds)) throw new Error('Products must be unique')
        console.log(`productIds`, productIds)
        const findAll = await strapi.db.query('api::product.product').findMany({
            where: {
                id: {
                    $in: productIds
                },
                deleted: {
                    $eq: false
                }
            }
        })
        if (findAll.length !== productIds.length) throw new Error('Some products do not exist')
        else return findAll
    }
}))
