{"kind": "collectionType", "collectionName": "booking_items", "info": {"singularName": "booking-item", "pluralName": "booking-items", "displayName": "BookingItem", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"quantity": {"type": "integer"}, "snapshotPrice": {"type": "decimal"}, "total": {"type": "decimal"}, "booking": {"type": "relation", "relation": "manyToOne", "target": "api::booking.booking", "inversedBy": "bookingItems"}, "productVariant": {"type": "relation", "relation": "oneToOne", "target": "api::product-variant.product-variant"}, "parentItem": {"type": "relation", "relation": "oneToOne", "target": "api::booking.booking"}}}