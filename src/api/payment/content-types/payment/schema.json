{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payment", "description": "Track individual payment transactions"}, "options": {"draftAndPublish": false}, "attributes": {"method": {"type": "enumeration", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "enumeration", "enum": ["unpaid", "paid", "refunded"], "default": "unpaid"}, "amount": {"type": "decimal", "required": true}, "paidAt": {"type": "datetime"}, "transactionReference": {"type": "string"}, "metadata": {"type": "json"}, "booking": {"type": "relation", "relation": "manyToOne", "target": "api::booking.booking", "inversedBy": "payments"}, "paymentInfo": {"type": "relation", "relation": "oneToOne", "target": "api::payment-info.payment-info"}}}