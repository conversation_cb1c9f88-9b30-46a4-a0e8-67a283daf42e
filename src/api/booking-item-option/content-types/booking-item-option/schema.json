{"kind": "collectionType", "collectionName": "booking_item_options", "info": {"singularName": "booking-item-option", "pluralName": "booking-item-options", "displayName": "BookingItemOption", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "booking": {"type": "relation", "relation": "manyToOne", "target": "api::booking.booking", "inversedBy": "selectedOptions"}, "optionValue": {"type": "relation", "relation": "manyToOne", "target": "api::option-value.option-value"}}}