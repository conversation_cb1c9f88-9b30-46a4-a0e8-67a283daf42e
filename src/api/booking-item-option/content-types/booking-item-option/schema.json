{"kind": "collectionType", "collectionName": "booking_item_options", "info": {"singularName": "booking-item-option", "pluralName": "booking-item-options", "displayName": "BookingItemOption", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "booking_item": {"type": "relation", "relation": "manyToOne", "target": "api::booking-item.booking-item", "inversedBy": "booking_item_options"}, "optionValue": {"type": "relation", "relation": "manyToOne", "target": "api::option-value.option-value"}, "product_variant": {"type": "relation", "relation": "oneToOne", "target": "api::product-variant.product-variant"}}}