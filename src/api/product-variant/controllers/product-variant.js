'use strict';

/**
 * product-variant controller
 */
const yup = require("yup")
const {generateProductVariantName, mergeIntoArray} = require("../../utils/product");

const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product-variant.product-variant', ({strapi}) => ({
    async create(ctx) {
        const {productId} = ctx.params
        const schema = yup.object().shape({
            key: yup.string().required(),
            value: yup.string().required(),
            overPrice: yup.number().required()
        })

        try {
            await schema.validate(ctx.request.body)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }

        const product = await strapi.db.query('api::product.product').findOne({
            where: {
                id: productId
            }
        })

        if (!product) {
            return ctx.badRequest('Product not found')
        }

        const {key, value, overPrice} = ctx.request.body

        const findExistingVariant = await strapi.db.query('api::product-variant.product-variant').findOne({
            where: {
                product: product,
                key: key,
                value: value
            }
        })

        const data = {
            name: generateProductVariantName(product, key, value),
            product: product,
            key: key,
            value: value,
            overPrice: overPrice
        }

        let needCreate = true
        let variant
        if (findExistingVariant) {
            if (findExistingVariant.deleted) {
                variant = await strapi.db.query('api::product-variant.product-variant').update({
                    where: {
                        id: findExistingVariant.id
                    },
                    data: {
                        ...data,
                        deleted: false,
                        deletedAt: null
                    }
                });
                needCreate = false
            } else {
                return ctx.badRequest('Variant already exists')
            }
        } else {
            if (needCreate) {
                variant = await strapi.db.query('api::product-variant.product-variant').create({
                    data: data
                })
            }

            const existingOptionRanges = product.optionRanges ?? {};

            mergeIntoArray(existingOptionRanges, {key: value})
            variant.product = await strapi.db.query('api::product.product').update({
                where: {
                    id: product.id
                },
                data: {
                    optionRanges: existingOptionRanges
                }
            })
            return variant
        }
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.find(ctx)
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.findOne(ctx)
    },
    async update(ctx) {
        const schema = yup.object().shape({
            overPrice: yup.number().required()
        }).noUnknown()
        try {
            await schema.validate(ctx.request.body)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }
        return super.update(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params
        await strapi.db.query('api::product-variant.product-variant').updateMany(
            {
                where: {
                    id: id
                },
                data: {
                    deleted: true,
                    deletedAt: new Date()
                }
            }
        )
    }
}))
