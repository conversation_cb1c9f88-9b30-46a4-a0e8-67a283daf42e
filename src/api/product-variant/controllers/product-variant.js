'use strict';

/**
 * product-variant controller
 */
const yup = require("yup")
const {generateProductVariantName, mergeIntoArray} = require("../../utils/product");

const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product-variant.product-variant', ({strapi}) => ({
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.find(ctx)
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.findOne(ctx)
    },
    async update(ctx) {
        const schema = yup.object().shape({
            price: yup.number(),
            name: yup.string(),
            displayName: yup.string(),
            available: yup.boolean(),
        }).noUnknown()
        try {
            await schema.validate(ctx.request.body)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }
        return super.update(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params
        await strapi.db.query('api::product-variant.product-variant').updateMany(
            {
                where: {
                    documentId: id
                },
                data: {
                    deleted: true,
                    deletedAt: new Date()
                }
            }
        )

        return true
    }
}))
