'use strict';

/**
 * product-variant router
 */

const {createCoreRouter} = require('@strapi/strapi').factories;

module.exports = {
    routes: [
        {
            method: 'PUT',
            path: '/product-variants/:id',
            handler: 'product-variant.update',
            config: {policies: []},
        },
        {
            method: 'DELETE',
            path: '/product-variants/:id',
            handler: 'product-variant.delete',
            config: {policies: []},
        },
        {
            method: 'GET',
            path: '/product-variants/:id',
            handler: 'product-variant.findOne',
            config: {
                policies: []
            },
        },
        {
            method: 'GET',
            path: '/product-variants',
            handler: 'product-variant.find',
            config: {
                policies: []
            },
        }
    ]
}