'use strict';

/**
 * product-variant router
 */

const {createCoreRouter} = require('@strapi/strapi').factories;

module.exports = {
    routes: [
        {
            method: 'POST',
            path: '/products/:productId/variant',
            handler: 'product-variant.create',
            config: {policies: []},
        },
        {
            method: 'PUT',
            path: '/products/:id',
            handler: 'product-variant.update',
            config: {policies: []},
        },
        {
            method: 'DELETE',
            path: '/products/:id',
            handler: 'product-variant.delete',
            config: {policies: []},
        },
        {
            method: 'GET',
            path: '/product-variants/:id',
            handler: 'product-variant.findOne',
            config: {
                policies: []
            },
        },
        {
            method: 'GET',
            path: '/product-variants',
            handler: 'product-variant.find',
            config: {
                policies: []
            },
        }
    ]
}