{"kind": "collectionType", "collectionName": "product_variants", "info": {"singularName": "product-variant", "pluralName": "product-variants", "displayName": "ProductVariant", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "overPrice": {"type": "decimal", "required": true, "default": 0}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "productVariants"}, "deleted": {"type": "boolean", "default": false, "required": true}, "deletedAt": {"type": "datetime"}, "available": {"type": "boolean", "default": false, "required": true}, "key": {"type": "string", "required": true}, "value": {"type": "string", "required": true}}}