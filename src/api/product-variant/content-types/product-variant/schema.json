{"kind": "collectionType", "collectionName": "product_variants", "info": {"singularName": "product-variant", "pluralName": "product-variants", "displayName": "ProductVariant", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "productVariants"}, "deleted": {"type": "boolean", "default": false, "required": true}, "deletedAt": {"type": "datetime"}, "available": {"type": "boolean", "default": false, "required": true}, "displayName": {"type": "string"}, "price": {"type": "decimal", "default": 0, "required": true}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::category.category", "inversedBy": "productVariants"}}}