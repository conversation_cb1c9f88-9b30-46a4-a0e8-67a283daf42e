{"kind": "collectionType", "collectionName": "booking_status_timelines", "info": {"singularName": "booking-status-timeline", "pluralName": "booking-status-timelines", "displayName": "BookingStatusTimeline"}, "options": {"draftAndPublish": false}, "attributes": {"fromStatus": {"type": "enumeration", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "enumeration", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "datetime"}, "note": {"type": "text"}, "changedBy": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "booking": {"type": "relation", "relation": "manyToOne", "target": "api::booking.booking", "inversedBy": "bookingStatusTimeline"}}}