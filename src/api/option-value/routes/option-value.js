'use strict';

/**
 * option-value router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = {
    routes: [
        {
            method: 'GET',
            path: '/option-values',
            handler: 'option-value.find',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/option-values/:id',
            handler: 'option-value.findOne',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'DELETE',
            path: '/option-values/:id',
            handler: 'option-value.delete',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/option-values/:id',
            handler: 'option-value.update',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/option-groups/:optionGroupId/option-values',
            handler: 'option-value.create',
            config: {
                policies: [],
                middlewares: [],
            },
        }
    ]
}
