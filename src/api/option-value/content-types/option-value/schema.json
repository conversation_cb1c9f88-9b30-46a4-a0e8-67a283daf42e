{"kind": "collectionType", "collectionName": "option_values", "info": {"singularName": "option-value", "pluralName": "option-values", "displayName": "OptionValue", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "group": {"type": "relation", "relation": "manyToOne", "target": "api::option-group.option-group", "inversedBy": "optionValues"}, "priceAdjustment": {"type": "integer"}, "deleted": {"type": "boolean", "default": false, "required": true}, "deletedAt": {"type": "datetime"}, "available": {"type": "boolean", "default": false, "required": true}}}