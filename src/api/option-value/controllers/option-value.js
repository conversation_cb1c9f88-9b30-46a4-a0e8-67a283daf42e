'use strict';
const {yup} = require("strapi-utils");

/**
 * option-value controller
 */

const {create<PERSON>oreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::option-value.option-value', ({strapi}) => ({
    async create(ctx) {
        const {group, ...data} = ctx.request.body.data;

        const optionValue = await strapi.db.query('api::option-value.option-value').create({
            data: {
                ...data,
                group: group.id,
            }
        });

        return {
            data: optionValue
        }
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return {
            data: super.find(ctx)
        }
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return {
            data: super.findOne(ctx)
        }
    },
    async update(ctx) {
        const schema = yup.object().shape({
            name: yup.string().required(),
            priceAdjustment: yup.number().default(0),
            available: yup.boolean(),
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        return {
            data: super.update(ctx)
        }
    },
    async delete(ctx) {
        const {id} = ctx.params
        return await strapi.db.query('api::option-value.option-value').update({
            where: {
                id: id
            },
            data: {
                deleted: true,
                deletedAt: new Date()
            }
        })
    }
}))
