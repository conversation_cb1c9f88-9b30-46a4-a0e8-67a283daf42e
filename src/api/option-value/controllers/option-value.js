'use strict';
const {yup} = require("strapi-utils");

/**
 * option-value controller
 */

const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::option-value.option-value', ({strapi}) => ({
    async create(ctx) {
        const schema = yup.object().shape({
            name: yup.string().required(),
            priceAdjustment: yup.number().default(0),
            displayName: yup.string(),
            available: yup.boolean(),
        }).noUnknown()

        const {optionGroupId} = ctx.params

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const optionGroup = await strapi.db.query('api::option-group.option-group').findOne({
            where: {
                id: optionGroupId,
                deleted: {
                    $eq: false
                }
            }
        })

        const data = ctx.request.body.data
        data.group = optionGroupId

        if(!optionGroup) {
            return ctx.badRequest('Option group not found')
        }

        return super.create(ctx)
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: {
                    $eq: false
                }
            }
        }
        return super.find(ctx)
    },
    async findOne(ctx) {
        const result = await super.findOne(ctx)
        if (result && result.data && result.data.deleted) {
            return ctx.badRequest('Option value not found')
        }
        return result
    },
    async update(ctx) {
        const schema = yup.object().shape({
            name: yup.string().required(),
            priceAdjustment: yup.number().default(0),
            available: yup.boolean(),
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const result = await super.findOne(ctx)

        if(result.data && result.data.deleted) {
            return ctx.badRequest('Option value not found')
        }

        return super.update(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params
        return await strapi.db.query('api::option-value.option-value').update({
            documentId: id,
            data: {
                deleted: true,
                deletedAt: new Date()
            }
        })
    }
}))
