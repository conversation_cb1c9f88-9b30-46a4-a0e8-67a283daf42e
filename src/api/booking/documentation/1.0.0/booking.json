{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "Booking API", "description": "Booking API documentation"}, "paths": {"/bookings": {"post": {"deprecated": false, "description": "Create a new booking", "summary": "Create a new booking", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [], "operationId": "post/bookings", "requestBody": {"description": "Create a new booking with product variants and option values", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewBookingRequest"}}}}}}}, "components": {"schemas": {"NewBookingRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["bookingItems", "scheduledTime"], "type": "object", "properties": {"bookingItems": {"type": "array", "items": {"type": "object", "required": ["productVariantId"], "properties": {"productVariantId": {"type": "integer", "description": "ID of the product variant to book"}, "optionValues": {"type": "array", "items": {"type": "integer"}, "description": "Array of option value IDs. If option group is single selection, only one option value per group is allowed."}}}, "description": "Array of booking items with product variants and optional option values"}, "scheduledTime": {"type": "string", "format": "date-time", "description": "Scheduled time for the booking (must be between now and next 4 hours) in UTC+7 timezone (Bangkok, Hanoi, Jakarta)"}, "paymentMethod": {"type": "string", "enum": ["cash", "momo", "vnpay"], "default": "cash", "description": "Payment method for the booking"}}}}, "example": {"data": {"bookingItems": [{"productVariantId": 1, "optionValues": [5, 8]}, {"productVariantId": 2, "optionValues": [6]}], "scheduledTime": "2023-06-15T14:30:00Z", "paymentMethod": "cash"}}}}}}