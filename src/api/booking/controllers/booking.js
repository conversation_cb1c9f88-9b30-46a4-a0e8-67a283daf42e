'use strict';

/**
 * booking controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const yup = require('yup');
const moment = require('moment-timezone');

// Set default timezone to UTC+7 (Bangkok, Hanoi, Jakarta)
const TIMEZONE = 'Asia/Bangkok';

module.exports = createCoreController('api::booking.booking', ({ strapi }) => ({
  /**
   * Update booking item quantities
   * @param {Object} ctx - The context object containing the request
   * @returns {Object} The updated booking or 204 if booking is deleted
   */
  async updateQuantity(ctx) {
    try {
      const { id } = ctx.params;

      // Define validation schema
      const schema = yup.object().shape({
        productVariants: yup.array().of(
          yup.object().shape({
            productVariantId: yup.number().required(),
            quantity: yup.number().min(0).required()
          })
        ).required()
      });

      // Validate request body
      await schema.validate(ctx.request.body.data);

      const { productVariants } = ctx.request.body.data;

      // Find the booking
      const booking = await strapi.entityService.findOne('api::booking.booking', id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'bookingItems.productVariant.product']
      });

      if (!booking) {
        return ctx.notFound('Booking not found');
      }

      // Check if booking can be updated (not CANCELLED or COMPLETED)
      if (booking.bookingStatus === 'CANCELLED' || booking.bookingStatus === 'COMPLETED') {
        return ctx.badRequest(`Cannot update a booking with status ${booking.bookingStatus}`);
      }

      // Process each product variant
      for (const item of productVariants) {
        const { productVariantId, quantity } = item;

        // Find the product variant
        const productVariant = await strapi.entityService.findOne('api::product-variant.product-variant', productVariantId, {
          populate: ['product']
        });

        if (!productVariant) {
          return ctx.badRequest(`Product variant with ID ${productVariantId} not found`);
        }

        // Check if product variant is not deleted and is available
        if (productVariant.deleted || !productVariant.available) {
          return ctx.badRequest(`Product variant with ID ${productVariantId} is not available`);
        }

        // Check if related product is not deleted and is available
        if (productVariant.product.deleted || !productVariant.product.available) {
          return ctx.badRequest(`Product with ID ${productVariant.product.id} is not available`);
        }

        // Find the booking item
        const bookingItem = booking.bookingItems.find(item => item.productVariant.id === productVariantId);

        if (!bookingItem) {
          // If booking item doesn't exist, create a new one
          if (quantity > 0) {
            // Calculate price for the new item
            const price = Number(productVariant.product.basePrice) + Number(productVariant.overPrice);

            // Create new booking item
            await strapi.entityService.create('api::booking-item.booking-item', {
              data: {
                booking: booking.id,
                productVariant: productVariantId,
                quantity: quantity,
                snapshotPrice: price,
                total: price,
                deleted: false
              }
            });
          }
          // If quantity is 0, no need to create a new booking item
        } else {
          // Update the existing booking item
          if (quantity === 0) {
            // If quantity is 0, mark as deleted
            await strapi.entityService.update('api::booking-item.booking-item', bookingItem.id, {
              data: {
                quantity: 0,
                total: 0,
                deleted: true
              }
            });
          } else {
            // Otherwise update the quantity and total
            await strapi.entityService.update('api::booking-item.booking-item', bookingItem.id, {
              data: {
                quantity,
                total: bookingItem.snapshotPrice,
                deleted: false
              }
            });
          }
        }
      }

      // Recalculate total amount
      const updatedBookingItems = await strapi.db.query('api::booking-item.booking-item').findMany({
        where: { booking: id, deleted: false }
      });

      // Check if all booking items are deleted
      if (updatedBookingItems.length === 0) {

        // Hard delete the booking
        await strapi.db.query('api::booking.booking').delete({
          where: { id }
        });

        // Return 204 No Content
        return ctx.send(null, 204);
      }


      // Return the updated booking wrapped in a data property
      const updatedBooking = await strapi.entityService.findOne('api::booking.booking', id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'payments']
      });

      return { data: updatedBooking };
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },

  /**
   * Create a booking
   * @param {Object} ctx - The context object containing the request
   * @returns {Object} The created booking
   */
  async create(ctx) {
    try {
      // Define validation schema
      const schema = yup.object().shape({
        bookingItems: yup.array().of(
          yup.object().shape({
            productVariantId: yup.number().required('Product variant ID is required'),
            optionValues: yup.array().of(yup.number())
          })
        ).min(1, 'At least one booking item is required').required('Booking items are required'),
        scheduledTime: yup.date().required('Scheduled time is required'),
        paymentMethod: yup.string().oneOf(['cash', 'momo', 'vnpay']).default('cash')
      });

      // Validate request body
      await schema.validate(ctx.request.body.data);

      const { bookingItems, scheduledTime, paymentMethod = 'cash' } = ctx.request.body.data;

      // Validate product variants and option values
      const validatedBookingItems = [];

      for (const item of bookingItems) {
        // Check if product variant exists, is not deleted, and is available
        const productVariant = await strapi.entityService.findOne('api::product-variant.product-variant', item.productVariantId, {
          populate: ['product']
        });

        if (!productVariant) {
          return ctx.badRequest(`Product variant with ID ${item.productVariantId} not found`);
        }

        if (productVariant.deleted || !productVariant.available) {
          return ctx.badRequest(`Product variant with ID ${item.productVariantId} is not available`);
        }

        if (productVariant.product.deleted || !productVariant.product.available) {
          return ctx.badRequest(`Product with ID ${productVariant.product.id} is not available`);
        }

        // Validate option values if provided
        const validatedOptionValues = [];

        if (item.optionValues && item.optionValues.length > 0) {
          // Group option values by option group to check single selection constraint
          const optionValuesByGroup = {};

          for (const optionValueId of item.optionValues) {
            // Check if option value exists, is not deleted, and is available
            const optionValue = await strapi.entityService.findOne('api::option-value.option-value', optionValueId, {
              populate: ['optionGroup']
            });

            if (!optionValue) {
              return ctx.badRequest(`Option value with ID ${optionValueId} not found`);
            }

            if (optionValue.deleted || !optionValue.available) {
              return ctx.badRequest(`Option value with ID ${optionValueId} is not available`);
            }

            if (!optionValue.optionGroup) {
              return ctx.badRequest(`Option value with ID ${optionValueId} has no option group`);
            }

            if (optionValue.optionGroup.deleted || !optionValue.optionGroup.available) {
              return ctx.badRequest(`Option group with ID ${optionValue.optionGroup.id} is not available`);
            }

            // Track option values by group for single selection validation
            const groupId = optionValue.optionGroup.id;
            if (!optionValuesByGroup[groupId]) {
              optionValuesByGroup[groupId] = {
                isSingleSelection: optionValue.optionGroup.selectionType === 'single',
                values: []
              };
            }

            optionValuesByGroup[groupId].values.push(optionValueId);

            // Add to validated option values
            validatedOptionValues.push(optionValueId);
          }

          // Check single selection constraint
          for (const groupId in optionValuesByGroup) {
            const group = optionValuesByGroup[groupId];
            if (group.isSingleSelection && group.values.length > 1) {
              return ctx.badRequest(`Option group ${groupId} is single selection but multiple values were provided`);
            }
          }
        }

        // Calculate price
        const price = Number(productVariant.product.basePrice) + Number(productVariant.overPrice);

        // Add validated item
        validatedBookingItems.push({
          productVariant: item.productVariantId,
          price,
          quantity: 1,
          optionValues: validatedOptionValues
        });
      }

      // Calculate total amount
      const totalAmount = validatedBookingItems.reduce((sum, item) => sum + item.price * item.quantity, 0);

      // Validate scheduled time (must be between now and next 4 hours)
      // Use moment.js to handle dates in UTC+7 timezone
      const now = moment().tz(TIMEZONE);
      const fourHoursLater = moment().tz(TIMEZONE).add(4, 'hours');

      // Parse the scheduledTime as UTC+7
      const scheduledDate = moment(scheduledTime).tz(TIMEZONE);

      // Log the times for debugging
      strapi.log.info(`Booking creation - Current time: ${now.format('YYYY-MM-DD HH:mm:ss')}, Scheduled time: ${scheduledDate.format('YYYY-MM-DD HH:mm:ss')}, Four hours later: ${fourHoursLater.format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

      // Check if scheduled time is valid
      if (scheduledDate.isBefore(now) || scheduledDate.isAfter(fourHoursLater)) {
        return ctx.badRequest(`Scheduled time must be between now (${now.format('YYYY-MM-DD HH:mm:ss')}) and the next 4 hours (${fourHoursLater.format('YYYY-MM-DD HH:mm:ss')}) (UTC+7)`);
      }

      // Get authenticated user
      const user = ctx.state.user;

      // Generate unique booking code using moment.js timestamp
      const timestamp = moment().tz(TIMEZONE).format('YYYYMMDDHHmmss');
      const bookingCode = `BK-${timestamp}-${Math.floor(Math.random() * 1000)}`;

      strapi.log.info(`Creating booking with code ${bookingCode} at ${now.format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

      // Create booking - convert moment objects to Date objects for database storage
      const booking = await strapi.entityService.create('api::booking.booking', {
        data: {
          code: bookingCode,
          scheduledTime: scheduledDate.toDate(), // Convert moment to Date
          bookedAt: now.toDate(), // Convert moment to Date
          totalAmount,
          bookingStatus: 'PENDING',
          user: user.id,
          publishedAt: now.toDate() // Convert moment to Date
        }
      });

      // Create booking items
      for (const item of validatedBookingItems) {
        // Create booking item
        const bookingItem = await strapi.entityService.create('api::booking-item.booking-item', {
          data: {
            booking: booking.id,
            productVariant: item.productVariant,
            quantity: item.quantity,
            snapshotPrice: item.price,
            total: item.price * item.quantity
          }
        });

        // Create booking item options if any
        if (item.optionValues && item.optionValues.length > 0) {
          for (const optionValueId of item.optionValues) {
            await strapi.entityService.create('api::booking-item-option.booking-item-option', {
              data: {
                productVariant: item.productVariant,
                bookingItem: bookingItem.id,
                optionValue: optionValueId
              }
            });
          }
        }
      }

      // Create payment record
      await strapi.entityService.create('api::payment.payment', {
        data: {
          method: paymentMethod,
          status: 'unpaid',
          amount: totalAmount,
          booking: booking.id
        }
      });


      // Send FCM notification
      await strapi.service('api::booking.booking').sendBookingNotification(booking);

      // Return the created booking wrapped in a data property
      const createdBooking = await strapi.entityService.findOne('api::booking.booking', booking.id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'payments']
      });

      return { data: createdBooking };
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  }
}));
