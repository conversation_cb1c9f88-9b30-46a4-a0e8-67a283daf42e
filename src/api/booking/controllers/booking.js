'use strict';

/**
 * booking controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const yup = require('yup');

module.exports = createCoreController('api::booking.booking', ({ strapi }) => ({
  /**
   * Create a booking
   * @param {Object} ctx - The context object containing the request
   * @returns {Object} The created booking
   */
  async create(ctx) {
    try {
      // Define validation schema
      const schema = yup.object().shape({
        productVariants: yup.array().of(yup.number()).min(1, 'At least one product variant is required').required(),
        scheduledTime: yup.date().required(),
        paymentMethod: yup.string().oneOf(['cash', 'momo', 'vnpay']).default('cash')
      });

      // Validate request body
      await schema.validate(ctx.request.body.data);

      const { productVariants, scheduledTime, paymentMethod = 'cash' } = ctx.request.body.data;

      // Validate scheduled time (must be between now and next 4 hours)
      const now = new Date();
      const fourHoursLater = new Date(now.getTime() + 4 * 60 * 60 * 1000);
      const scheduledDate = new Date(scheduledTime);

      if (scheduledDate < now || scheduledDate > fourHoursLater) {
        return ctx.badRequest('Scheduled time must be between now and the next 4 hours');
      }

      // Validate product variants
      const validProductVariants = [];
      let totalAmount = 0;

      // Check each product variant
      for (const variantId of productVariants) {
        // Get product variant with its related product
        const productVariant = await strapi.entityService.findOne('api::product-variant.product-variant', variantId, {
          populate: ['product']
        });

        // Check if product variant exists
        if (!productVariant) {
          return ctx.badRequest(`Product variant with ID ${variantId} not found`);
        }

        // Check if product variant is not deleted and is available
        if (productVariant.deleted || !productVariant.available) {
          return ctx.badRequest(`Product variant with ID ${variantId} is not available`);
        }

        // Check if related product is not deleted and is available
        if (productVariant.product.deleted || !productVariant.product.available) {
          return ctx.badRequest(`Product with ID ${productVariant.product.id} is not available`);
        }

        // Calculate price
        const price = Number(productVariant.product.basePrice) + Number(productVariant.overPrice);

        validProductVariants.push({
          productVariant: variantId,
          price,
          quantity: 1
        });

        totalAmount += price;
      }

      // Get authenticated user
      const user = ctx.state.user;

      // Generate unique booking code
      const bookingCode = `BK-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Create booking
      const booking = await strapi.entityService.create('api::booking.booking', {
        data: {
          code: bookingCode,
          scheduledTime: scheduledDate,
          bookedAt: now,
          totalAmount,
          bookingStatus: 'PENDING',
          user: user.id,
          publishedAt: now
        }
      });

      // Create booking items
      for (const item of validProductVariants) {
        await strapi.entityService.create('api::booking-item.booking-item', {
          data: {
            booking: booking.id,
            productVariant: item.productVariant,
            quantity: item.quantity,
            snapshotPrice: item.price,
            total: item.price * item.quantity
          }
        });
      }

      // Create payment record
      await strapi.entityService.create('api::payment.payment', {
        data: {
          method: paymentMethod,
          status: 'unpaid',
          amount: totalAmount,
          booking: booking.id
        }
      });

      // Create booking status timeline
      await strapi.entityService.create('api::booking-status-timeline.booking-status-timeline', {
        data: {
          toStatus: 'PENDING',
          changedAt: now,
          booking: booking.id
        }
      });

      // Send FCM notification
      await strapi.service('api::booking.booking').sendBookingNotification(booking);

      // Return the created booking
      return await strapi.entityService.findOne('api::booking.booking', booking.id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'payments']
      });
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  }
}));
