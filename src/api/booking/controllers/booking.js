'use strict';

/**
 * booking controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const yup = require('yup');

module.exports = createCoreController('api::booking.booking', ({ strapi }) => ({
  /**
   * Update booking item quantities
   * @param {Object} ctx - The context object containing the request
   * @returns {Object} The updated booking or 204 if booking is deleted
   */
  async updateQuantity(ctx) {
    try {
      const { id } = ctx.params;

      // Define validation schema
      const schema = yup.object().shape({
        productVariants: yup.array().of(
          yup.object().shape({
            productVariant_id: yup.number().required(),
            quantity: yup.number().min(0).required()
          })
        ).required()
      });

      // Validate request body
      await schema.validate(ctx.request.body.data);

      const { productVariants } = ctx.request.body.data;

      // Find the booking
      const booking = await strapi.entityService.findOne('api::booking.booking', id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'bookingItems.productVariant.product']
      });

      if (!booking) {
        return ctx.notFound('Booking not found');
      }

      // Check if booking can be updated (not CANCELLED or COMPLETED)
      if (booking.bookingStatus === 'CANCELLED' || booking.bookingStatus === 'COMPLETED') {
        return ctx.badRequest(`Cannot update a booking with status ${booking.bookingStatus}`);
      }

      // Process each product variant
      for (const item of productVariants) {
        const { productVariant_id, quantity } = item;

        // Find the product variant
        const productVariant = await strapi.entityService.findOne('api::product-variant.product-variant', productVariant_id, {
          populate: ['product']
        });

        if (!productVariant) {
          return ctx.badRequest(`Product variant with ID ${productVariant_id} not found`);
        }

        // Check if product variant is not deleted and is available
        if (productVariant.deleted || !productVariant.available) {
          return ctx.badRequest(`Product variant with ID ${productVariant_id} is not available`);
        }

        // Check if related product is not deleted and is available
        if (productVariant.product.deleted || !productVariant.product.available) {
          return ctx.badRequest(`Product with ID ${productVariant.product.id} is not available`);
        }

        // Find the booking item
        const bookingItem = booking.bookingItems.find(item => item.productVariant.id === productVariant_id);

        if (!bookingItem) {
          return ctx.badRequest(`Booking item with product variant ID ${productVariant_id} not found in this booking`);
        }

        // Update the booking item
        if (quantity === 0) {
          // If quantity is 0, mark as deleted
          await strapi.entityService.update('api::booking-item.booking-item', bookingItem.id, {
            data: {
              quantity: 0,
              total: 0,
              deleted: true
            }
          });
        } else {
          // Otherwise update the quantity and total
          await strapi.entityService.update('api::booking-item.booking-item', bookingItem.id, {
            data: {
              quantity,
              total: bookingItem.snapshotPrice * quantity,
              deleted: false
            }
          });
        }
      }

      // Recalculate total amount
      const updatedBookingItems = await strapi.db.query('api::booking-item.booking-item').findMany({
        where: { booking: id, deleted: false }
      });

      // Check if all booking items are deleted
      if (updatedBookingItems.length === 0) {
        // Create booking status timeline for cancellation
        await strapi.entityService.create('api::booking-status-timeline.booking-status-timeline', {
          data: {
            fromStatus: booking.bookingStatus,
            toStatus: 'CANCELLED',
            changedAt: new Date(),
            note: 'All items removed from booking',
            booking: id
          }
        });

        // Hard delete the booking
        await strapi.db.query('api::booking.booking').delete({
          where: { id }
        });

        // Return 204 No Content
        return ctx.send(null, 204);
      }

      // Calculate new total amount
      const totalAmount = updatedBookingItems.reduce((sum, item) => sum + Number(item.total), 0);

      // Update booking total amount
      await strapi.entityService.update('api::booking.booking', id, {
        data: {
          totalAmount
        }
      });

      // Return the updated booking
      return await strapi.entityService.findOne('api::booking.booking', id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'payments']
      });
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },

  /**
   * Create a booking
   * @param {Object} ctx - The context object containing the request
   * @returns {Object} The created booking
   */
  async create(ctx) {
    try {
      // Define validation schema
      const schema = yup.object().shape({
        productVariants: yup.array().of(yup.number()).min(1, 'At least one product variant is required').required(),
        scheduledTime: yup.date().required(),
        paymentMethod: yup.string().oneOf(['cash', 'momo', 'vnpay']).default('cash')
      });

      // Validate request body
      await schema.validate(ctx.request.body.data);

      const { productVariants, scheduledTime, paymentMethod = 'cash' } = ctx.request.body.data;

      // Validate scheduled time (must be between now and next 4 hours)
      const now = new Date();
      const fourHoursLater = new Date(now.getTime() + 4 * 60 * 60 * 1000);
      const scheduledDate = new Date(scheduledTime);
      console.log(now)

      if (scheduledDate < now || scheduledDate > fourHoursLater) {
        return ctx.badRequest('Scheduled time must be between now and the next 4 hours');
      }

      // Validate product variants
      const validProductVariants = [];
      let totalAmount = 0;

      // Check each product variant
      for (const variantId of productVariants) {
        // Get product variant with its related product
        const productVariant = await strapi.entityService.findOne('api::product-variant.product-variant', variantId, {
          populate: ['product']
        });

        // Check if product variant exists
        if (!productVariant) {
          return ctx.badRequest(`Product variant with ID ${variantId} not found`);
        }

        // Check if product variant is not deleted and is available
        if (productVariant.deleted || !productVariant.available) {
          return ctx.badRequest(`Product variant with ID ${variantId} is not available`);
        }

        // Check if related product is not deleted and is available
        if (productVariant.product.deleted || !productVariant.product.available) {
          return ctx.badRequest(`Product with ID ${productVariant.product.id} is not available`);
        }

        // Calculate price
        const price = Number(productVariant.product.basePrice) + Number(productVariant.overPrice);

        validProductVariants.push({
          productVariant: variantId,
          price,
          quantity: 1
        });

        totalAmount += price;
      }

      // Get authenticated user
      const user = ctx.state.user;

      // Generate unique booking code
      const bookingCode = `BK-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Create booking
      const booking = await strapi.entityService.create('api::booking.booking', {
        data: {
          code: bookingCode,
          scheduledTime: scheduledDate,
          bookedAt: now,
          totalAmount,
          bookingStatus: 'PENDING',
          user: user.id,
          publishedAt: now
        }
      });

      // Create booking items
      for (const item of validProductVariants) {
        await strapi.entityService.create('api::booking-item.booking-item', {
          data: {
            booking: booking.id,
            productVariant: item.productVariant,
            quantity: item.quantity,
            snapshotPrice: item.price,
            total: item.price * item.quantity
          }
        });
      }

      // Create payment record
      await strapi.entityService.create('api::payment.payment', {
        data: {
          method: paymentMethod,
          status: 'unpaid',
          amount: totalAmount,
          booking: booking.id
        }
      });

      // Create booking status timeline
      await strapi.entityService.create('api::booking-status-timeline.booking-status-timeline', {
        data: {
          toStatus: 'PENDING',
          changedAt: now,
          booking: booking.id
        }
      });

      // Send FCM notification
      await strapi.service('api::booking.booking').sendBookingNotification(booking);

      // Return the created booking
      return await strapi.entityService.findOne('api::booking.booking', booking.id, {
        populate: ['bookingItems', 'bookingItems.productVariant', 'payments']
      });
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  }
}));
