'use strict';

/**
 * booking router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

// Create the default router
const defaultRouter = createCoreRouter('api::booking.booking');

// Add custom routes
const customRoutes = [
  {
    method: 'POST',
    path: '/bookings',
    handler: 'booking.create',
    config: {
      policies: [],
      middlewares: [],
    },
  },
  {
    method: 'POST',
    path: '/bookings/:id/quantity',
    handler: 'booking.updateQuantity',
    config: {
      policies: [],
      middlewares: [],
    },
  },
];

// Export the combined routes
module.exports = {
  routes: [
    ...customRoutes,
  ],
};
