{"kind": "collectionType", "collectionName": "bookings", "info": {"singularName": "booking", "pluralName": "bookings", "displayName": "Booking", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"code": {"type": "uid", "required": true}, "bookingStatus": {"type": "enumeration", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"], "default": "PENDING"}, "bookedAt": {"type": "datetime"}, "scheduledTime": {"type": "datetime"}, "totalAmount": {"type": "decimal"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "bookingItems": {"type": "relation", "relation": "oneToMany", "target": "api::booking-item.booking-item", "mappedBy": "booking"}, "payments": {"type": "relation", "relation": "oneToMany", "target": "api::payment.payment", "mappedBy": "booking"}, "bookingStatusTimeline": {"type": "relation", "relation": "oneToMany", "target": "api::booking-status-timeline.booking-status-timeline", "mappedBy": "booking"}}}