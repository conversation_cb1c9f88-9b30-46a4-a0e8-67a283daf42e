'use strict';

/**
 * booking service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::booking.booking', ({ strapi }) => ({
  /**
   * Send FCM notification for a new booking
   * @param {Object} booking - The booking object
   * @returns {Promise<void>}
   */
  async sendBookingNotification(booking) {
    // TODO: Implement FCM notification sending
    // This function will send notifications to both the customer and admin/staff
    // 1. Get the user's FCM token from their profile
    // 2. Get admin/staff FCM tokens
    // 3. Prepare notification payload
    // 4. Send notifications using Firebase Admin SDK

    strapi.log.info(`Notification would be sent for booking: ${booking.code}`);

    // Return a resolved promise for now
    return Promise.resolve();
  }
}));
