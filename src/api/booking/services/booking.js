'use strict';

/**
 * booking service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const moment = require('moment-timezone');

// Set default timezone to UTC+7 (Bangkok, Hanoi, Jakarta)
const TIMEZONE = 'Asia/Bangkok';

module.exports = createCoreService('api::booking.booking', ({ strapi }) => ({
  /**
   * Send FCM notification for a new booking
   * @param {Object} booking - The booking object
   * @returns {Promise<void>}
   */
  async sendBookingNotification(booking) {
    // TODO: Implement FCM notification sending
    // This function will send notifications to both the customer and admin/staff
    // 1. Get the user's FCM token from their profile
    // 2. Get admin/staff FCM tokens
    // 3. Prepare notification payload
    // 4. Send notifications using Firebase Admin SDK

    strapi.log.info(`Notification would be sent for booking: ${booking.code}`);

    // Return a resolved promise for now
    return Promise.resolve();
  },

  async sendCancelledBookingNotification(booking) {
    // TODO: Implement FCM notification sending
    // This function will send notifications to both the customer and admin/staff
    // 1. Get the user's FCM token from their profile
    // 2. Get admin/staff FCM tokens
    // 3. Prepare notification payload
    // 4. Send notifications using Firebase Admin SDK

    strapi.log.info(`Notification would be sent for cancelled booking: ${booking.code}`);

    // Return a resolved promise for now
    return Promise.resolve();
  },
  /**
   * Cancel a booking and create a status timeline entry
   * @param {number} bookingId - The ID of the booking to cancel
   * @param {string} note - The reason for cancellation
   * @returns {Promise<Object>} The updated booking
   */
  async cancelBooking(bookingId, note) {
    try {
      // Get the current booking to check its status
      const booking = await strapi.entityService.findOne('api::booking.booking', bookingId);

      if (!booking) {
        throw new Error(`Booking with ID ${bookingId} not found`);
      }

      // Get current time in UTC+7 using moment
      const now = this.getUTC7Time();

      strapi.log.info(`Cancelling booking ${bookingId} at ${moment(now).tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

      // Create booking status timeline entry
      await strapi.entityService.create('api::booking-status-timeline.booking-status-timeline', {
        data: {
          fromStatus: booking.bookingStatus,
          toStatus: 'CANCELLED',
          changedAt: now,
          note: note || 'Booking cancelled',
          booking: bookingId
        }
      });

      // Update booking status to CANCELLED
      const updatedBooking = await strapi.entityService.update('api::booking.booking', bookingId, {
        data: {
          bookingStatus: 'CANCELLED'
        }
      });

      // Send notification about cancellation
      await this.sendCancelledBookingNotification(updatedBooking);

      return updatedBooking;
    } catch (error) {
      strapi.log.error(`Error cancelling booking ${bookingId}: ${error.message}`);
      throw error;
    }
  }
}));
