module.exports = {
    generateProductVariantName: (product, metadata) => {
        // for every key in metadata to generate name for example : name: [key1: value1], [key2: value2]
        return `${product.name} - ${Object.entries(metadata).map(([key, value]) => `${key}: ${value}`).join(', ')}`;
    },
    mergeIntoArray: (obj1, obj2) => {
        for (const [key, value] of Object.entries(obj2)) {
            if (obj1.hasOwnProperty(key)) {
                const existing = obj1[key];
                if (Array.isArray(existing)) {
                    existing.push(value);
                } else {
                    obj1[key] = [existing, value];
                }
            } else {
                obj1[key] = [value];
            }
        }
    },
    defaultVariantValueName: () => 'Option 1',
}