module.exports = {
    generateProductVariantName: (product, key, value) => {
        return `${product.name}: ${key} / ${value}`
    },
    mergeIntoArray: (obj1, obj2) => {
        for (const [key, value] of Object.entries(obj2)) {
            if (obj1.hasOwnProperty(key)) {
                const existing = obj1[key];
                if (Array.isArray(existing)) {
                    existing.push(value);
                } else {
                    obj1[key] = [existing, value];
                }
            } else {
                obj1[key] = [value];
            }
        }
    },
    defaultVariantValueName: () => 'Option 1',
}