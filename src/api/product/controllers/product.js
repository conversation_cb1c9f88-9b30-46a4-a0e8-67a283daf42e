'use strict';

/**
 * product controller
 */

const yup = require("yup")
const {defaultVariantValueName} = require("../../utils/product");
const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product.product', ({strapi}) => ({
    async create(ctx) {
        const schema = yup.object().shape({
            name: yup.string().required(),
            basePrice: yup.number().required(),
            type: yup.string().oneOf(['single', 'variant']).required(),
            options: yup.array().of(yup.string())
        }).noUnknown()

        try {
            console.log(ctx.request.body.data)
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }

        const options = ctx.request.body.data.options ?? ['Default Variant'];


        const data = ctx.request.body.data

        // delete options key
        delete data.options
        const product = await strapi.db.query('api::product.product').create(
            {
                data: data,
                populate: {
                    ...ctx.query.populate,
                },
            }
        )

        product.options = await strapi.service('api::product.product').createDefaultProductVariants(product,
            options, ctx.request.body.data.type)

        return {
            'data': product
        }
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            populate: {
                ...ctx.query.populate,
                productVariants: true
            },
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.findOne(ctx)
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.find(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params

        await strapi.db.query('api::product.product').update(
            {
                where: {
                    documentId: id,
                },
                data: {
                    deleted: true,
                    deletedAt: new Date()
                }
            }
        )

        return true
    },
    async update(ctx) {
        const schema = yup.object().shape({
            name: yup.string(),
            description: yup.string(),
            basePrice: yup.number(),
            type: yup.string().oneOf(['service', 'product']),
            available: yup.boolean()
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }

        const find = await strapi.db.query('api::product.product').findOne({
            where: {
                documentId: ctx.params.id
            }
        })

        if (!find || find.deleted) {
            return ctx.badRequest('Product not found')
        }

        return super.update(ctx)
    }
}));