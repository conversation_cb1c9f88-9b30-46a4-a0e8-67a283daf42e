'use strict';

/**
 * product controller
 */

const yup = require("yup")
const {defaultVariantValueName} = require("../../utils/product");
const {createCoreController} = require('@strapi/strapi').factories;

module.exports = createCoreController('api::product.product', ({strapi}) => ({
    async create(ctx) {
        function testUnique(arr) {
            return Array.isArray(arr) && new Set(arr).size === arr.length
        }

        const schema = yup.object().shape({
            name: yup.string().required(),
            description: yup.string().required(),
            basePrice: yup.number().required(),
            type: yup.string().oneOf(['service', 'product']).required(),
            options: yup.array().of(yup.string()).default(['Default Variant']).min(1)
                .test('unique', 'Options must be unique', testUnique).required()
        }).noUnknown()

        try {
            console.log(ctx.request.body.data)
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }


        const optionRanges = {}

        ctx.request.body.data.options.forEach((option) => {
            optionRanges[option] = [defaultVariantValueName()]
        })

        const product = await strapi.db.query('api::product.product').create(
            {
                data: {
                    ...ctx.request.body.data,
                    optionRanges: optionRanges
                },
                populate: {
                    ...ctx.query.populate,
                },
            }
        )

        await strapi.service('api::product.product').createDefaultProductVariants(product,
            ctx.request.body.data.options, ctx.request.body.data.type)
        return {
            'data': product
        }
    },
    async findOne(ctx) {
        ctx.query = {
            ...ctx.query,
            populate: {
                ...ctx.query.populate,
                productVariants: true
            },
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.findOne(ctx)
    },
    async find(ctx) {
        ctx.query = {
            ...ctx.query,
            filters: {
                ...ctx.query.filters,
                deleted: false
            }
        }
        return super.find(ctx)
    },
    async delete(ctx) {
        const {id} = ctx.params
        await strapi.db.query('api::product-variant.product-variant').updateMany(
            {
                where: {
                    product: id
                },
                data: {
                    deleted: true,
                    deletedAt: new Date()
                }
            }
        )

        await strapi.db.query('api::product.product').update(
            {
                where: {
                    id: id
                },
                data: {
                    deleted: true,
                    deletedAt: new Date()
                }
            }
        )
    },
    async update(ctx) {
        const schema = yup.object().shape({
            name: yup.string(),
            description: yup.string().required(),
            basePrice: yup.number().required(),
            type: yup.string().oneOf(['service', 'product']).required(),
            available: yup.boolean()
        }).noUnknown()
        try {
            await schema.validate(ctx.request.body)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }
        return super.update(ctx)
    },
    async editOptionRangesValue(ctx) {
        const {productId} = ctx.params
        const schema = yup.object().shape({
            key: yup.string().required(),
            oldValue: yup.string().required(),
            newValue: yup.string().required(),
        }).noUnknown()

        const data = ctx.request.body.data

        try {
            await schema.validate(data)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }

        const {key, oldValue, newValue} = data

        const product = await strapi.db.query('api::product.product').findOne({
            where: {
                id: productId
            }
        })
        if (!product) {
            return ctx.notFound('Product not found')
        }

        if (!product.optionRanges.hasOwnProperty(key) || !product.optionRanges[key].includes(oldValue)) {
            return ctx.badRequest('Key does not exist or value does not exist')
        }

        return await strapi.service('api::product.product').editOptionsRangeValue(product, key, oldValue, newValue)
    },
    async createNewProductVariant(ctx) {
        const {productId} = ctx.params

        const schema = yup.object().shape({
            key: yup.string().required(),
            value: yup.string().required(),
            overPrice: yup.number()
        }).noUnknown()

        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest('Invalid request body')
        }

        const data = ctx.request.body.data

        const product = await strapi.db.query('api::product.product').findOne({
            where: {
                id: productId
            }
        })

        if (!product) {
            return ctx.notFound('Product not found')
        }

        if (product.optionRanges.hasOwnProperty(data.key) && product.optionRanges[data.key].includes(data.value)) {
            return ctx.badRequest('Key/value pair already exists')
        }

        return {
            'data': await strapi.service('api::product.product').createNewProductVariant(product, ctx.request.body.data)
        }
    },
    async editOptionRangesKey(ctx) {
        const {productId} = ctx.params
        const schema = yup.object().shape({
            oldKey: yup.string().required(),
            newKey: yup.string().required(),
        }).noUnknown()
        try {
            await schema.validate(ctx.request.body.data)
        } catch (e) {
            return ctx.badRequest(e.toString())
        }
        const product = await strapi.db.query('api::product.product').findOne({
            where: {
                id: productId
            }
        })
        if (!product) {
            return ctx.notFound('Product not found')
        }
        if(!product.optionRanges.hasOwnProperty(ctx.request.body.data.oldKey)) {
            return ctx.badRequest('Key does not exist')
        }

        return {
            'data': await strapi.service('api::product.product').editOptionRangesKey(product, ctx.request.body.data.oldKey, ctx.request.body.data.newKey)
        }
    }
}));