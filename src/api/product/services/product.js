'use strict';

const {generateProductVariantName, mergeIntoArray, defaultVariantValueName} = require("../../utils/product");
/**
 * product service
 */

const {createCoreService} = require('@strapi/strapi').factories;

module.exports = createCoreService('api::product.product', ({strapi}) => ({
    async createDefaultProductVariants(product, options, type) {
        console.log(`options`, options)
        const promises = []
        options.forEach(option => {
            console.log(`option`, option)
            promises.push(strapi.db.query('api::product-variant.product-variant').create({
                data: {
                    name: `${product.name} - ${option}`,
                    displayName: `${option}`,
                    product: product.id,
                    price: product.price
                }
            }))
        })
        await Promise.all(promises)
    },
    // async editOptionRangesKey(product, oldKey, newKey) {
    //
    //     const existingOptionRanges = product.optionRanges ?? {};
    //
    //     existingOptionRanges[newKey] = existingOptionRanges[oldKey]
    //     delete existingOptionRanges[oldKey]
    //
    //     console.log(`existingOptionRanges`, existingOptionRanges)
    //
    //     await strapi.db.query('api::product.product').update({
    //         where: {
    //             id: product.id
    //         },
    //         data: {
    //             optionRanges: existingOptionRanges
    //         }
    //     })
    //
    //     const list = await strapi.db.query('api::product-variant.product-variant').findMany({
    //         where: {
    //             product: {
    //                 $eq: product.id
    //             },
    //             key: {
    //                 $eq: oldKey
    //             }
    //         }
    //     })
    //
    //     return await Promise.all(list.map(async item => {
    //         return await strapi.db.query('api::product-variant.product-variant').update({
    //             where: {
    //                 id: item.id
    //             },
    //             data: {
    //                 name: generateProductVariantName(
    //                     product,
    //                     newKey,
    //                     item.value),
    //                 key: newKey
    //             }
    //         })
    //     }))
    // },
    // async editOptionsRangeValue(product, key, oldValue, newValue) {
    //
    //     const existingOptionRanges = product.optionRanges ?? {};
    //     existingOptionRanges[key] = existingOptionRanges[key].map(item => item === oldValue ? newValue : item)
    //
    //     await strapi.db.query('api::product.product').update({
    //         where: {
    //             id: product.id
    //         },
    //         data: {
    //             optionRanges: existingOptionRanges
    //         }
    //     })
    //
    //     await strapi.db.query('api::product.product').update({
    //         where: {
    //             id: product.id
    //         },
    //         data: {
    //             optionRanges: {
    //                 ...product.optionRanges,
    //                 [key]: product.optionRanges[key].map(item => item === oldValue ? newValue : item)
    //             }
    //         }
    //     })
    //
    //     const list = await strapi.db.query('api::product-variant.product-variant').findMany({
    //         where: {
    //             product: {
    //                 $eq: product.id
    //             },
    //             key: {
    //                 $eq: key
    //             }
    //         }
    //     })
    //     return await Promise.all(list.map(async item => {
    //         return await strapi.db.query('api::product-variant.product-variant').update({
    //             where: {
    //                 id: item.id
    //             },
    //             data: {
    //                 name: generateProductVariantName(
    //                     product,
    //                     key,
    //                     newValue),
    //                 value: newValue
    //             }
    //         })
    //     }))
    // },
    // async createNewProductVariant(product, data) {
    //     const {key, value, overPrice} = data;
    //
    //     const findExistingVariant = await strapi.db.query('api::product-variant.product-variant').findOne({
    //         where: {
    //             product: {
    //                 $eq: product.id
    //             },
    //             key: {
    //                 $eq: key
    //             },
    //             value: {
    //                 $eq: value
    //             }
    //         }
    //     })
    //
    //     console.log(findExistingVariant)
    //
    //     if (!findExistingVariant) {
    //         await strapi.db.query('api::product-variant.product-variant').create({
    //             data: {
    //                 name: generateProductVariantName(product, key, value),
    //                 product: product,
    //                 key: key,
    //                 value: value,
    //                 overPrice: overPrice,
    //             }
    //         })
    //     }
    //
    //     console.log(findExistingVariant)
    //
    //     await this.mergeIntoArray(
    //         product,
    //         {
    //             [key]: value
    //         })
    //
    //     return await strapi.db.query('api::product.product').findOne({
    //         where: {
    //             id: product.id
    //         },
    //         populate: {
    //             productVariants: true
    //         }
    //     })
    // },
    // async mergeIntoArray(product, newOptionRange) {
    //     const existingOptionRanges = product.optionRanges ?? {};
    //
    //     mergeIntoArray(existingOptionRanges, newOptionRange)
    //
    //     await strapi.db.query('api::product.product').update({
    //         where: {
    //             id: product.id
    //         },
    //         data: {
    //             optionRanges: existingOptionRanges
    //         }
    //     })
    // }
}))