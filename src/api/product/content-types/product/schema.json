{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "type": {"type": "enumeration", "enum": ["service", "product"]}, "productVariants": {"type": "relation", "relation": "oneToMany", "target": "api::product-variant.product-variant", "mappedBy": "product"}, "deleted": {"type": "boolean", "default": false, "required": true}, "deletedAt": {"type": "datetime"}, "basePrice": {"type": "decimal"}, "available": {"type": "boolean", "default": false, "required": true}, "isVariantBasedProduct": {"type": "boolean", "default": false, "required": true}, "options": {"type": "relation", "relation": "manyToMany", "target": "api::option-group.option-group", "mappedBy": "products"}}}