'use strict';

/**
 * product router
 */

const {createCoreRouter} = require('@strapi/strapi').factories;

module.exports = {
    routes: [
        //get
        {
            method: 'GET',
            path: '/products',
            handler: 'product.find',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        //get by id
        {
            method: 'GET',
            path: '/products/:id',
            handler: 'product.findOne',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'DELETE',
            path: '/products/:id',
            handler: 'product.delete',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/products',
            handler: 'product.create',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/products/:id',
            handler: 'product.update',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/products/:productId/variant',
            handler: 'product.createNewProductVariant',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/products/:productId/variant-key',
            handler: 'product.editOptionRangesKey',
            config: {
                policies: [],
                middlewares: [],
            }
        },
        {
            method: 'PUT',
            path: '/products/:productId/variant-value',
            handler: 'product.editOptionRangesValue',
            config: {
                policies: [],
                middlewares: [],
            }
        }
    ]
}
