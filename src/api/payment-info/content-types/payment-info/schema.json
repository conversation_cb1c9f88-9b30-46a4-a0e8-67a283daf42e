{"kind": "collectionType", "collectionName": "payment_infos", "info": {"singularName": "payment-info", "pluralName": "payment-infos", "displayName": "PaymentInfo"}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "metadata": {"type": "json"}, "method": {"type": "enumeration", "enum": ["momo", "vnPay"]}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}