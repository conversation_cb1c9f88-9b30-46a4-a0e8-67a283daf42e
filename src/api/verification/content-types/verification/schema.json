{"kind": "collectionType", "collectionName": "verifications", "info": {"singularName": "verification", "pluralName": "verifications", "displayName": "Verification", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"verificationSid": {"type": "uid", "required": false}, "verificationStatus": {"type": "enumeration", "enum": ["pending", "approved"]}, "type": {"type": "enumeration", "enum": ["confirmAccount", "deleteAccount"]}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "dialCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "deleteAccountOtpExpires": {"type": "datetime"}, "verificationMethod": {"type": "enumeration", "enum": ["phone", "email"]}}}