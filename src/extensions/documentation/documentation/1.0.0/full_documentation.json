{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "DOCUMENTATION", "description": "", "termsOfService": "YOUR_TERMS_OF_SERVICE_URL", "contact": {"name": "TEAM", "email": "<EMAIL>", "url": "mywebsite.io"}, "license": {"name": "Apache 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}, "x-generation-date": "2025-05-21T04:09:54.610Z"}, "x-strapi-config": {"plugins": ["upload", "users-permissions"]}, "servers": [{"url": "http://localhost:1337/api", "description": "Development server"}], "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"}, "security": [{"bearerAuth": []}], "paths": {"/bookings": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/bookings"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [], "operationId": "post/bookings", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingRequest"}}}}}}, "/bookings/{id}/quantity": {"post": {"summary": "Update booking item quantities", "description": "Update quantities of product variants in a booking. If quantity is 0, the item will be marked as deleted. If all items are deleted, the booking will be cancelled and hard-deleted.", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponse"}}}}, "204": {"description": "No Content - Booking was cancelled and deleted because all items were removed"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [{"name": "id", "in": "path", "description": "ID of the booking to update", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "post/bookings/{id}/quantity", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingQuantityUpdateRequest"}, "example": {"data": {"productVariants": [{"productVariant_id": 1, "quantity": 2}, {"productVariant_id": 2, "quantity": 0}]}}}}}}}, "/bookings/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/bookings/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/bookings/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/bookings/{id}"}}, "/booking-items": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-item"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/booking-items"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-item"], "parameters": [], "operationId": "post/booking-items", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemRequest"}}}}}}, "/booking-items/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-item"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/booking-items/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-item"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/booking-items/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingItemRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-item"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/booking-items/{id}"}}, "/booking-status-timelines": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-status-timeline"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/booking-status-timelines"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-status-timeline"], "parameters": [], "operationId": "post/booking-status-timelines", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineRequest"}}}}}}, "/booking-status-timelines/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-status-timeline"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/booking-status-timelines/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-status-timeline"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/booking-status-timelines/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookingStatusTimelineRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Booking-status-timeline"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/booking-status-timelines/{id}"}}, "/payments": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/payments"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment"], "parameters": [], "operationId": "post/payments", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}}}, "/payments/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/payments/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/payments/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/payments/{id}"}}, "/payment-infos": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment-info"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/payment-infos"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment-info"], "parameters": [], "operationId": "post/payment-infos", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoRequest"}}}}}}, "/payment-infos/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment-info"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/payment-infos/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment-info"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/payment-infos/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInfoRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Payment-info"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/payment-infos/{id}"}}, "/phones": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Phone"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/phones"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Phone"], "parameters": [], "operationId": "post/phones", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneRequest"}}}}}}, "/phones/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Phone"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/phones/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Phone"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/phones/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Phone"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/phones/{id}"}}, "/products": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/products"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [], "operationId": "post/products", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}}}}, "/products/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/products/{id}"}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/products/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/products/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}}}}, "/products/{productId}/variant": {"post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "productId", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "post/products/{productId}/variant", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}}}}, "/products/{productId}/variant-key": {"put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "productId", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/products/{productId}/variant-key", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}}}}, "/products/{productId}/variant-value": {"put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product"], "parameters": [{"name": "productId", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/products/{productId}/variant-value", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}}}}, "/product-variants/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductVariantResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product-variant"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/product-variants/{id}"}}, "/product-variants": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductVariantListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Product-variant"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/product-variants"}}, "/verifications": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Verification"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/verifications"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Verification"], "parameters": [], "operationId": "post/verifications", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationRequest"}}}}}}, "/verifications/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Verification"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/verifications/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Verification"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/verifications/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Verification"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/verifications/{id}"}}, "/upload": {"post": {"description": "Upload files", "responses": {"200": {"description": "response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "summary": "", "tags": ["Upload - File"], "requestBody": {"description": "Upload files", "required": true, "content": {"multipart/form-data": {"schema": {"required": ["files"], "type": "object", "properties": {"path": {"type": "string", "description": "The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."}, "refId": {"type": "string", "description": "The ID of the entry which the file(s) will be linked to"}, "ref": {"type": "string", "description": "The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."}, "field": {"type": "string", "description": "The field of the entry which the file(s) will be precisely linked to."}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}}}, "/upload?id={id}": {"post": {"parameters": [{"name": "id", "in": "query", "description": "File id", "required": true, "schema": {"type": "string"}}], "description": "Upload file information", "responses": {"200": {"description": "response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "summary": "", "tags": ["Upload - File"], "requestBody": {"description": "Upload files", "required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"fileInfo": {"type": "object", "properties": {"name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}}}, "files": {"type": "string", "format": "binary"}}}}}}}}, "/upload/files": {"get": {"tags": ["Upload - File"], "responses": {"200": {"description": "Get a list of files", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}}}, "/upload/files/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "string"}}], "tags": ["Upload - File"], "responses": {"200": {"description": "Get a specific file", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "delete": {"parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "string"}}], "tags": ["Upload - File"], "responses": {"200": {"description": "Delete a file", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadFile"}}}}}}}, "/connect/{provider}": {"get": {"parameters": [{"name": "provider", "in": "path", "required": true, "description": "Provider name", "schema": {"type": "string", "pattern": ".*"}}], "tags": ["Users-Permissions - Auth"], "summary": "Login with a provider", "description": "Redirects to provider login before being redirect to /auth/{provider}/callback", "responses": {"301": {"description": "Redirect response"}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/local": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Local login", "description": "Returns a jwt token and user info", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"identifier": {"type": "string"}, "password": {"type": "string"}}}, "example": {"identifier": "foobar", "password": "Test1234"}}}, "required": true}, "responses": {"200": {"description": "Connection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/local/register": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Register a user", "description": "Returns a jwt token and user info", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foobar", "email": "<EMAIL>", "password": "Test1234"}}}, "required": true}, "responses": {"200": {"description": "Successful registration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/{provider}/callback": {"get": {"tags": ["Users-Permissions - Auth"], "summary": "Default Callback from provider auth", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "Provider name", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/forgot-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Send rest password email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}, "example": {"email": "<EMAIL>"}}}}, "responses": {"200": {"description": "Returns ok", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/reset-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Rest user password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"password": {"type": "string"}, "passwordConfirmation": {"type": "string"}, "code": {"type": "string"}}}, "example": {"password": "Test1234", "passwordConfirmation": "Test1234", "code": "zertyoaizndoianzodianzdonaizdoinaozdnia"}}}}, "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/change-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Update user's own password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["password", "currentPassword", "passwordConfirmation"], "properties": {"password": {"type": "string"}, "currentPassword": {"type": "string"}, "passwordConfirmation": {"type": "string"}}}}}}, "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/email-confirmation": {"get": {"tags": ["Users-Permissions - Auth"], "summary": "Confirm user email", "parameters": [{"in": "query", "name": "confirmation", "schema": {"type": "string"}, "description": "confirmation token received by email"}], "responses": {"301": {"description": "Redirects to the configure email confirmation redirect url"}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/send-email-confirmation": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Send confirmation email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Returns email and boolean to confirm email was sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "sent": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/permissions": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get default generated permissions", "responses": {"200": {"description": "Returns the permissions tree", "content": {"application/json": {"schema": {"type": "object", "properties": {"permissions": {"$ref": "#/components/schemas/Users-Permissions-PermissionsTree"}}}, "example": {"permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": false, "policy": ""}, "findOne": {"enabled": false, "policy": ""}, "create": {"enabled": false, "policy": ""}}, "controllerB": {"find": {"enabled": false, "policy": ""}, "findOne": {"enabled": false, "policy": ""}, "create": {"enabled": false, "policy": ""}}}}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "List roles", "responses": {"200": {"description": "Returns list of roles", "content": {"application/json": {"schema": {"type": "object", "properties": {"roles": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-Role"}, {"type": "object", "properties": {"nb_users": {"type": "number"}}}]}}}}, "example": {"roles": [{"id": 1, "name": "Public", "description": "Default role given to unauthenticated user.", "type": "public", "createdAt": "2022-05-19T17:35:35.097Z", "updatedAt": "2022-05-31T16:05:36.603Z", "nb_users": 0}]}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Create a role", "requestBody": {"$ref": "#/components/requestBodies/Users-Permissions-RoleRequest"}, "responses": {"200": {"description": "Returns ok if the role was create", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles/{id}": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get a role", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "responses": {"200": {"description": "Returns the role", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}, "example": {"role": {"id": 1, "name": "Public", "description": "Default role given to unauthenticated user.", "type": "public", "createdAt": "2022-05-19T17:35:35.097Z", "updatedAt": "2022-05-31T16:05:36.603Z", "permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": true}}}}}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles/{role}": {"put": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Update a role", "parameters": [{"in": "path", "name": "role", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "requestBody": {"$ref": "#/components/requestBodies/Users-Permissions-RoleRequest"}, "responses": {"200": {"description": "Returns ok if the role was udpated", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Delete a role", "parameters": [{"in": "path", "name": "role", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "responses": {"200": {"description": "Returns ok if the role was delete", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get list of users", "responses": {"200": {"description": "Returns an array of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Users-Permissions-User"}}, "example": [{"id": 9, "username": "<EMAIL>", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-06-01T18:32:35.211Z", "updatedAt": "2022-06-01T18:32:35.217Z"}]}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Create a user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"email": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foo", "email": "<EMAIL>", "password": "foo-password"}}}}, "responses": {"201": {"description": "Returns created user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}, {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z", "role": {"id": 1, "name": "X", "description": "Default role given to authenticated user.", "type": "authenticated", "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-06-04T07:11:59.551Z"}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/{id}": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "responses": {"200": {"description": "Returns a user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-User"}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Update a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"email": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foo", "email": "<EMAIL>", "password": "foo-password"}}}}, "responses": {"200": {"description": "Returns updated user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}, {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z", "role": {"id": 1, "name": "X", "description": "Default role given to authenticated user.", "type": "authenticated", "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-06-04T07:11:59.551Z"}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Delete a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "responses": {"200": {"description": "Returns deleted user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/me": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get authenticated user info", "responses": {"200": {"description": "Returns user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-User"}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/count": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get user count", "responses": {"200": {"description": "Returns a number", "content": {"application/json": {"schema": {"type": "number"}, "example": 1}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Error": {"type": "object", "required": ["error"], "properties": {"data": {"nullable": true, "oneOf": [{"type": "object"}, {"type": "array", "items": {"type": "object"}}]}, "error": {"type": "object", "properties": {"status": {"type": "integer"}, "name": {"type": "string"}, "message": {"type": "string"}, "details": {"type": "object"}}}}}, "BookingRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["code"], "type": "object", "properties": {"code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "bookingItems": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "payments": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "bookingStatusTimeline": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "BookingQuantityUpdateRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["productVariants"], "type": "object", "properties": {"productVariants": {"type": "array", "items": {"type": "object", "required": ["productVariant_id", "quantity"], "properties": {"productVariant_id": {"type": "integer", "description": "ID of the product variant to update"}, "quantity": {"type": "integer", "minimum": 0, "description": "New quantity for the product variant. If 0, the item will be marked as deleted."}}}, "description": "Array of product variants with their new quantities"}}}}, "example": {"data": {"productVariants": [{"productVariant_id": 1, "quantity": 2}, {"productVariant_id": 2, "quantity": 0}]}}}, "BookingListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Booking"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "Booking": {"type": "object", "required": ["code"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "bookingItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "bookingItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "paymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "bookingStatusTimeline": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "productVariant": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "parentItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "bookingStatusTimeline": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "BookingResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Booking"}, "meta": {"type": "object"}}}, "BookingItemRequest": {"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "properties": {"quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "productVariant": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "parentItem": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "BookingItemListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BookingItem"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "BookingItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "bookingItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "productVariant": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "parentItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "paymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "bookingStatusTimeline": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "productVariant": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "parentItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "BookingItemResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BookingItem"}, "meta": {"type": "object"}}}, "BookingStatusTimelineRequest": {"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "properties": {"fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "booking": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "BookingStatusTimelineListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BookingStatusTimeline"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "BookingStatusTimeline": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "bookingItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "productVariant": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "parentItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "paymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "bookingStatusTimeline": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "BookingStatusTimelineResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BookingStatusTimeline"}, "meta": {"type": "object"}}}, "PaymentRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["amount"], "type": "object", "properties": {"method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "paymentInfo": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "PaymentListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Payment"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "Payment": {"type": "object", "required": ["amount"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "bookingStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "PREPARING", "READY", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "bookedAt": {"type": "string", "format": "date-time"}, "scheduledTime": {"type": "string", "format": "date-time"}, "totalAmount": {"type": "number", "format": "float"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "bookingItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "quantity": {"type": "integer"}, "snapshotPrice": {"type": "number", "format": "float"}, "total": {"type": "number", "format": "float"}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "productVariant": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "parentItem": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "method": {"type": "string", "enum": ["cash", "momo", "vnpay"]}, "status": {"type": "string", "enum": ["unpaid", "paid", "refunded"]}, "amount": {"type": "number", "format": "float"}, "paidAt": {"type": "string", "format": "date-time"}, "transactionReference": {"type": "string"}, "metadata": {}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "paymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "bookingStatusTimeline": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "fromStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "toStatus": {"type": "string", "enum": ["PENDING", "CONFIRMED", "IN_PROGRESS", "CANCELLED", "COMPLETED"]}, "changedAt": {"type": "string", "format": "date-time"}, "note": {"type": "string"}, "changedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "booking": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "paymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "PaymentResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Payment"}, "meta": {"type": "object"}}}, "PaymentInfoRequest": {"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "properties": {"name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "PaymentInfoListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentInfo"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "PaymentInfo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "metadata": {}, "method": {"type": "string", "enum": ["momo", "vnPay"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "PaymentInfoResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaymentInfo"}, "meta": {"type": "object"}}}, "PhoneRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["phoneNumber", "dialCode"], "type": "object", "properties": {"user": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "phoneNumber": {"type": "string"}, "dialCode": {"type": "string"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "PhoneListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Phone"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "Phone": {"type": "object", "required": ["phoneNumber", "dialCode"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "phoneNumber": {"type": "string"}, "dialCode": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "phoneNumber": {"type": "string"}, "dialCode": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "PhoneResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Phone"}, "meta": {"type": "object"}}}, "ProductRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["name", "deleted", "available"], "type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "ProductListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "Product": {"type": "object", "required": ["name", "deleted", "available"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "ProductResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Product"}, "meta": {"type": "object"}}}, "ProductVariantRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["name", "overPrice", "deleted", "available", "key", "value"], "type": "object", "properties": {"name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "ProductVariantListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductVariant"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "ProductVariant": {"type": "object", "required": ["name", "overPrice", "deleted", "available", "key", "value"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "optionRanges": {}, "type": {"type": "string", "enum": ["service", "product"]}, "productVariants": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "overPrice": {"type": "number", "format": "float"}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "basePrice": {"type": "number", "format": "float"}, "available": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "available": {"type": "boolean"}, "key": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "ProductVariantResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ProductVariant"}, "meta": {"type": "object"}}}, "VerificationRequest": {"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "properties": {"verificationSid": {"type": "string"}, "verificationStatus": {"type": "string", "enum": ["pending", "approved"]}, "type": {"type": "string", "enum": ["confirmAccount", "deleteAccount"]}, "user": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "dialCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "deleteAccountOtpExpires": {"type": "string", "format": "date-time"}, "verificationMethod": {"type": "string", "enum": ["phone", "email"]}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "VerificationListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Verification"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "Verification": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "verificationSid": {"type": "string"}, "verificationStatus": {"type": "string", "enum": ["pending", "approved"]}, "type": {"type": "string", "enum": ["confirmAccount", "deleteAccount"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "displayName": {"type": "string"}, "deleted": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "dialCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "deleteAccountOtpExpires": {"type": "string", "format": "date-time"}, "verificationMethod": {"type": "string", "enum": ["phone", "email"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "verificationSid": {"type": "string"}, "verificationStatus": {"type": "string", "enum": ["pending", "approved"]}, "type": {"type": "string", "enum": ["confirmAccount", "deleteAccount"]}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "dialCode": {"type": "string"}, "phoneNumber": {"type": "string"}, "deleteAccountOtpExpires": {"type": "string", "format": "date-time"}, "verificationMethod": {"type": "string", "enum": ["phone", "email"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "VerificationResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/Verification"}, "meta": {"type": "object"}}}, "UploadFile": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "number", "format": "integer"}, "height": {"type": "number", "format": "integer"}, "formats": {"type": "number"}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "double"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {"type": "object"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Users-Permissions-Role": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Users-Permissions-User": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "foo.bar"}, "email": {"type": "string", "example": "<EMAIL>"}, "provider": {"type": "string", "example": "local"}, "confirmed": {"type": "boolean", "example": true}, "blocked": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2022-06-02T08:32:06.258Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2022-06-02T08:32:06.267Z"}}}, "Users-Permissions-UserRegistration": {"type": "object", "properties": {"jwt": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}, "user": {"$ref": "#/components/schemas/Users-Permissions-User"}}}, "Users-Permissions-PermissionsTree": {"type": "object", "additionalProperties": {"type": "object", "description": "every api", "properties": {"controllers": {"description": "every controller of the api", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"description": "every action of every controller", "type": "object", "properties": {"enabled": {"type": "boolean"}, "policy": {"type": "string"}}}}}}}}}, "requestBodies": {"Users-Permissions-RoleRequest": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"$ref": "#/components/schemas/Users-Permissions-PermissionsTree"}}}, "example": {"name": "foo", "description": "role foo", "permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": true}}}}}}}}}}}, "tags": [{"name": "Users-Permissions - Auth", "description": "Authentication endpoints", "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}, {"name": "Users-Permissions - Users & Roles", "description": "Users, roles, and permissions endpoints", "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]}