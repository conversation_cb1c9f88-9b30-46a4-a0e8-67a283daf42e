#!/bin/bash
BASE_URL="http://localhost:1337/api"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzQ2MTkxODg0LCJleHAiOjE3NDg3ODM4ODR9.cwUBVd9SKO-3wYNQlr2COQg1ajhZjrr5Rmh_MyyqHQA"

echo 'Creating Services and Variants...'
echo "Creating service: Car Wash"
curl --location "$BASE_URL/services" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Car Wash\", \"description\": \"Full exterior and interior wash\", \"type\": \"service\", \"optionRanges\": {\"type\": [\"5 seated\", \"7 seated\"], \"clean_option\": [\"interior\", \"exterior\", \"all\"]}, \"availability\": true}}"

echo "Creating variant: Car Wash - 5 seated - Interior"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Car Wash - 5 seated - Interior\", \"price\": 150000, \"currency\": \"vnd\", \"metadata\": {\"type\": \"5 seated\", \"clean_option\": \"interior\"}, \"service\": \"1\"}}"

echo "Creating variant: Car Wash - 7 seated - All"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Car Wash - 7 seated - All\", \"price\": 180000, \"currency\": \"vnd\", \"metadata\": {\"type\": \"7 seated\", \"clean_option\": \"all\"}, \"service\": \"1\"}}"

echo "Creating service: Oil Change"
curl --location "$BASE_URL/services" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Oil Change\", \"description\": \"Engine oil replacement service\", \"type\": \"service\", \"optionRanges\": {\"grade\": [\"standard\", \"premium\"]}, \"availability\": true}}"

echo "Creating variant: Oil Change - Standard"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Oil Change - Standard\", \"price\": 250000, \"currency\": \"vnd\", \"metadata\": {\"grade\": \"standard\"}, \"service\": \"2\"}}"

echo "Creating variant: Oil Change - Premium"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Oil Change - Premium\", \"price\": 400000, \"currency\": \"vnd\", \"metadata\": {\"grade\": \"premium\"}, \"service\": \"2\"}}"

echo "Creating service: Perfume"
curl --location "$BASE_URL/services" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Perfume\", \"description\": \"Car perfume add-on\", \"type\": \"product\", \"optionRanges\": {\"fragrance_type\": [\"vanilla\", \"ocean breeze\"]}, \"availability\": true}}"

echo "Creating variant: Perfume - Vanilla"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Perfume - Vanilla\", \"price\": 100000, \"currency\": \"vnd\", \"metadata\": {\"fragrance_type\": \"vanilla\"}, \"service\": \"3\"}}"

echo "Creating variant: Perfume - Ocean Breeze"
curl --location "$BASE_URL/service-variants" \
--header "Content-Type: application/json" \
--header "Authorization: Bearer $TOKEN" \
--data "{\"data\": {\"name\": \"Perfume - Ocean Breeze\", \"price\": 120000, \"currency\": \"vnd\", \"metadata\": {\"fragrance_type\": \"ocean breeze\"}, \"service\": \"3\"}}"
