'use strict';

/**
 * Cron tasks configuration
 */

module.exports = {
  /**
   * Cron job to check for expired pending bookings
   * Runs every minute to find bookings that are approaching their 30-minute expiration time
   * and schedules them to be cancelled exactly at the 30-minute mark
   */
  expirePendingBookings: {
    task: async ({ strapi }) => {
      try {
        // Get current time
        const now = new Date();

        // Find pending bookings that are not deleted and are approaching their 30-minute expiration
        // We look for bookings that will expire in the next minute

        // Find bookings where:
        // 1. Status is PENDING
        // 2. createdAt + 30 minutes is between now and now + 1 minute
        // This means we're looking for bookings that will expire in the next minute
        const bookingsToExpire = await strapi.db.query('api::booking.booking').findMany({
          where: {
            bookingStatus: 'PENDING',
            createdAt: {
              // Find bookings created between 29 and 30 minutes ago
              $lt: new Date(now.getTime() - 29 * 60 * 1000), // created more than 29 minutes ago
              $gt: new Date(now.getTime() - 30 * 60 * 1000)  // but less than 30 minutes ago
            }
          },
          // Make sure we have all the data we need
          populate: ['bookingStatusTimeline']
        });

        // Process each booking that needs to be expired
        for (const booking of bookingsToExpire) {
          // Calculate the exact time when the booking should be cancelled (30 minutes after creation)
          const createdAt = new Date(booking.createdAt);
          const expirationTime = new Date(createdAt.getTime() + 30 * 60 * 1000);

          // Calculate delay in milliseconds until the exact expiration time
          const delayMs = expirationTime.getTime() - now.getTime();

          if (delayMs > 0) {
            // Schedule the cancellation to happen at exactly 30 minutes after creation
            setTimeout(async () => {
              try {
                // Double-check that the booking still exists and is still in PENDING status
                const currentBooking = await strapi.db.query('api::booking.booking').findOne({
                  where: { id: booking.id }
                });

                if (currentBooking && currentBooking.bookingStatus === 'PENDING') {
                  // Use the booking service to cancel the booking
                  await strapi.service('api::booking.booking').cancelBooking(
                    booking.id,
                    'Automatically cancelled after 30 minutes of inactivity'
                  );

                  strapi.log.info(`Booking ${booking.code} (ID: ${booking.id}) automatically cancelled after 30 minutes`);
                }
              } catch (error) {
                strapi.log.error(`Error cancelling expired booking ${booking.id}: ${error.message}`);
              }
            }, delayMs);

            strapi.log.info(`Scheduled booking ${booking.code} (ID: ${booking.id}) to be cancelled in ${delayMs}ms`);
          }
        }
      } catch (error) {
        strapi.log.error(`Error in expirePendingBookings cron job: ${error.message}`);
      }
    },
    options: {
      rule: '* * * * *', // Run every minute
      tz: 'Asia/Bangkok' // Use UTC+7 timezone (Bangkok, Hanoi, Jakarta)
    }
  }
};
