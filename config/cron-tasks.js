'use strict';

/**
 * Cron tasks configuration
 */

const moment = require('moment-timezone');

// Set default timezone to UTC+7 (Bangkok, Hanoi, Jakarta)
const TIMEZONE = 'Asia/Bangkok';

module.exports = {
  /**
   * Cron job to check for expired pending bookings
   * Runs every minute to find bookings that are approaching their 30-minute expiration time
   * and schedules them to be cancelled exactly at the 30-minute mark
   */
  expirePendingBookings: {
    task: async ({ strapi }) => {
      try {
        // Get current time in UTC+7 using moment
        const now = moment().tz(TIMEZONE).toDate();

        strapi.log.info(`Running booking expiration check at ${moment(now).format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

        // Get the booking service for helper methods
        const bookingService = strapi.service('api::booking.booking');

        // Find pending bookings that are not deleted and are approaching their 30-minute expiration
        // We look for bookings that will expire in the next minute

        // Find bookings where:
        // 1. Status is PENDING
        // 2. createdAt + 30 minutes is between now and now + 1 minute
        // This means we're looking for bookings that will expire in the next minute

        // Calculate the time ranges in UTC+7 using moment
        const twentyNineMinutesAgo = moment(now).subtract(29, 'minutes').toDate();
        const thirtyMinutesAgo = moment(now).subtract(30, 'minutes').toDate();

        strapi.log.info(`Looking for bookings created between ${moment(thirtyMinutesAgo).format('YYYY-MM-DD HH:mm:ss')} and ${moment(twentyNineMinutesAgo).format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

        const bookingsToExpire = await strapi.db.query('api::booking.booking').findMany({
          where: {
            bookingStatus: 'PENDING',
            createdAt: {
              // Find bookings created between 29 and 30 minutes ago
              $lt: twentyNineMinutesAgo, // created more than 29 minutes ago
              $gt: thirtyMinutesAgo      // but less than 30 minutes ago
            }
          },
          // Make sure we have all the data we need
          populate: ['bookingStatusTimeline']
        });

        // Process each booking that needs to be expired
        for (const booking of bookingsToExpire) {
          // Calculate the exact time when the booking should be cancelled (30 minutes after creation)
          // Convert booking.createdAt to a moment object in UTC+7
          const createdAt = moment(booking.createdAt).tz(TIMEZONE);

          // Calculate the exact expiration time (30 minutes after creation) in UTC+7
          const expirationTime = moment(createdAt).add(30, 'minutes');

          // Log the times for debugging
          strapi.log.info(`Booking ${booking.code} (ID: ${booking.id}): Created at ${createdAt.format('YYYY-MM-DD HH:mm:ss')}, expires at ${expirationTime.format('YYYY-MM-DD HH:mm:ss')} (UTC+7)`);

          // Calculate delay in milliseconds until the exact expiration time
          const delayMs = expirationTime.diff(moment().tz(TIMEZONE));

          if (delayMs > 0) {
            // Schedule the cancellation to happen at exactly 30 minutes after creation
            setTimeout(async () => {
              try {
                // Get the current time in UTC+7 for logging
                const cancelTime = moment().tz(TIMEZONE).format('YYYY-MM-DD HH:mm:ss');

                // Double-check that the booking still exists and is still in PENDING status
                const currentBooking = await strapi.db.query('api::booking.booking').findOne({
                  where: { id: booking.id }
                });

                if (currentBooking && currentBooking.bookingStatus === 'PENDING') {
                  strapi.log.info(`Executing scheduled cancellation for booking ${booking.code} (ID: ${booking.id}) at ${cancelTime} (UTC+7)`);

                  // Use the booking service to cancel the booking
                  await bookingService.cancelBooking(
                    booking.id,
                    'Automatically cancelled after 30 minutes of inactivity'
                  );

                  strapi.log.info(`Booking ${booking.code} (ID: ${booking.id}) automatically cancelled after 30 minutes`);
                } else {
                  strapi.log.info(`Skipping cancellation for booking ${booking.code} (ID: ${booking.id}) at ${cancelTime} - status is no longer PENDING`);
                }
              } catch (error) {
                strapi.log.error(`Error cancelling expired booking ${booking.id}: ${error.message}`);
              }
            }, delayMs);

            strapi.log.info(`Scheduled booking ${booking.code} (ID: ${booking.id}) to be cancelled in ${delayMs}ms`);
          }
        }
      } catch (error) {
        strapi.log.error(`Error in expirePendingBookings cron job: ${error.message}`);
      }
    },
    options: {
      rule: '* * * * *', // Run every minute
      tz: 'Asia/Bangkok' // Use UTC+7 timezone (Bangkok, Hanoi, Jakarta)
    }
  }
};
