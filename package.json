{"name": "chargechill-be", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "@strapi/plugin-cloud": "5.12.7", "@strapi/plugin-users-permissions": "5.12.7", "@strapi/strapi": "5.12.7", "@strapi/utils": "^5.12.7", "archiver": "^7.0.1", "better-sqlite3": "11.3.0", "firebase-admin": "^13.3.0", "json2csv": "^6.0.0-alpha.2", "mime-types": "^2.1.27", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "pg": "^8.15.6", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-utils": "^3.6.11", "styled-components": "^6.0.0", "twilio": "^5.6.0", "yup": "^1.6.1"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "36ce2e4d4611e7030a54bf0bbb2918d41b9ecf9eb42acf2646c94a32791ad7fd"}}